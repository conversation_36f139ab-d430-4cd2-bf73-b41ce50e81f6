# SDL平台功能模块详细设计

## 1. 代码安全扫描模块

### 1.1 静态应用安全测试（SAST）模块

#### 1.1.1 功能概述
静态代码安全分析模块负责在不执行代码的情况下，通过分析源代码、字节码或二进制文件来识别安全漏洞。

#### 1.1.2 核心功能
- **多语言支持**：Java、Python、JavaScript、C#、Go、PHP、C/C++等
- **规则引擎**：支持自定义安全规则和OWASP规则集
- **增量扫描**：只扫描变更的代码，提升扫描效率
- **误报过滤**：智能误报识别和过滤机制

#### 1.1.3 集成工具
- **SonarQube**：企业级代码质量和安全平台
- **CodeQL**：GitHub的语义代码分析引擎
- **Checkmarx**：商业SAST解决方案
- **Semgrep**：开源静态分析工具

#### 1.1.4 实现架构
```java
@Service
public class SastScanService {
    
    @Autowired
    private ScanTaskRepository scanTaskRepository;
    
    @Autowired
    private SonarQubeClient sonarQubeClient;
    
    @Autowired
    private CodeQLClient codeQLClient;
    
    public ScanResult executeSastScan(ScanRequest request) {
        // 1. 创建扫描任务
        ScanTask task = createScanTask(request);
        
        // 2. 选择扫描引擎
        ScanEngine engine = selectScanEngine(request.getLanguage());
        
        // 3. 执行扫描
        RawScanResult rawResult = engine.scan(request);
        
        // 4. 结果标准化
        ScanResult standardResult = normalizeResult(rawResult);
        
        // 5. 保存结果
        saveScanResult(task, standardResult);
        
        return standardResult;
    }
}
```

### 1.2 动态应用安全测试（DAST）模块

#### 1.2.1 功能概述
动态安全测试模块通过模拟攻击者的行为，对运行中的应用程序进行黑盒测试，发现运行时安全漏洞。

#### 1.2.2 核心功能
- **Web应用扫描**：HTTP/HTTPS协议的Web应用安全测试
- **API安全测试**：RESTful API和GraphQL接口安全测试
- **认证测试**：登录绕过、会话管理漏洞检测
- **业务逻辑测试**：业务流程中的安全缺陷检测

#### 1.2.3 集成工具
- **OWASP ZAP**：开源Web应用安全扫描器
- **Burp Suite**：专业Web安全测试工具
- **Nessus**：网络漏洞扫描器
- **Nuclei**：快速漏洞扫描器

#### 1.2.4 扫描策略配置
```yaml
dast_config:
  target_url: "https://example.com"
  scan_policy: "full_scan"
  authentication:
    type: "form_based"
    login_url: "/login"
    username_field: "username"
    password_field: "password"
    credentials:
      username: "testuser"
      password: "testpass"
  scan_scope:
    include_urls:
      - "https://example.com/api/*"
      - "https://example.com/admin/*"
    exclude_urls:
      - "https://example.com/logout"
  scan_options:
    max_scan_time: 3600  # 1小时
    max_depth: 10
    concurrent_requests: 5
```

### 1.3 软件组成分析（SCA）模块

#### 1.3.1 功能概述
SCA模块专门检测项目中使用的开源组件和第三方库的已知安全漏洞，确保供应链安全。

#### 1.3.2 核心功能
- **依赖解析**：自动识别项目依赖关系
- **漏洞数据库**：集成CVE、NVD等漏洞数据库
- **许可证检查**：开源许可证合规性检查
- **版本管理**：组件版本过期和升级建议

#### 1.3.3 支持的包管理器
- **JavaScript**：npm (package.json), yarn (yarn.lock)
- **Python**：pip (requirements.txt), pipenv (Pipfile)
- **Java**：Maven (pom.xml), Gradle (build.gradle)
- **Go**：Go Modules (go.mod)
- **Ruby**：Bundler (Gemfile)
- **PHP**：Composer (composer.json)

#### 1.3.4 实现示例
```java
@Component
public class ScaScanEngine {
    
    public ScaResult scanDependencies(Project project) {
        List<Dependency> dependencies = parseDependencies(project);
        List<Vulnerability> vulnerabilities = new ArrayList<>();
        
        for (Dependency dep : dependencies) {
            // 查询漏洞数据库
            List<Vulnerability> depVulns = vulnerabilityDatabase
                .findByComponent(dep.getName(), dep.getVersion());
            vulnerabilities.addAll(depVulns);
        }
        
        return new ScaResult(dependencies, vulnerabilities);
    }
    
    private List<Dependency> parseDependencies(Project project) {
        String projectType = detectProjectType(project);
        DependencyParser parser = getParser(projectType);
        return parser.parse(project.getSourcePath());
    }
}
```

## 2. 漏洞管理和跟踪系统

### 2.1 漏洞生命周期管理

#### 2.1.1 状态流转模型
```mermaid
stateDiagram-v2
    [*] --> 新发现
    新发现 --> 已确认: 安全工程师确认
    新发现 --> 误报: 标记为误报
    已确认 --> 修复中: 分配给开发者
    修复中 --> 已修复: 开发者修复完成
    已修复 --> 已验证: 安全工程师验证
    已修复 --> 修复中: 验证失败
    已验证 --> 已关闭: 最终确认
    误报 --> [*]
    已关闭 --> [*]
```

#### 2.1.2 优先级评估算法
```java
public class VulnerabilityPriorityCalculator {
    
    public Priority calculatePriority(Vulnerability vuln) {
        int score = 0;
        
        // CVSS基础分数 (40%)
        score += vuln.getCvssScore() * 4;
        
        // 业务影响 (30%)
        score += getBusinessImpact(vuln) * 3;
        
        // 可利用性 (20%)
        score += getExploitability(vuln) * 2;
        
        // 资产重要性 (10%)
        score += getAssetCriticality(vuln) * 1;
        
        return Priority.fromScore(score);
    }
}
```

### 2.2 误报管理机制

#### 2.2.1 误报申请流程
1. **开发者申请**：开发者标记漏洞为误报并提供理由
2. **自动预检**：系统进行初步的误报检查
3. **安全审核**：安全工程师人工审核
4. **决策记录**：记录审核决策和理由
5. **规则更新**：将确认的误报加入过滤规则

#### 2.2.2 智能误报识别
```python
class FalsePositiveDetector:
    def __init__(self):
        self.ml_model = load_trained_model()
        self.rule_engine = RuleEngine()
    
    def is_false_positive(self, vulnerability):
        # 基于规则的检查
        if self.rule_engine.check(vulnerability):
            return True
        
        # 机器学习模型预测
        features = self.extract_features(vulnerability)
        confidence = self.ml_model.predict_proba(features)[0][1]
        
        return confidence > 0.8
    
    def extract_features(self, vuln):
        return {
            'file_type': vuln.file_path.split('.')[-1],
            'line_context': vuln.code_context,
            'vulnerability_type': vuln.type,
            'tool_confidence': vuln.confidence
        }
```

## 3. 安全策略配置和合规检查

### 3.1 策略配置引擎

#### 3.1.1 策略模板系统
```yaml
# OWASP Top 10 策略模板
policy_template:
  name: "OWASP_TOP_10_2021"
  version: "1.0"
  description: "基于OWASP Top 10 2021的安全策略"
  
  rules:
    - id: "A01_BROKEN_ACCESS_CONTROL"
      name: "失效的访问控制"
      severity: "HIGH"
      enabled: true
      checks:
        - type: "sast"
          patterns:
            - "authorization bypass"
            - "privilege escalation"
        - type: "dast"
          tests:
            - "forced browsing"
            - "parameter manipulation"
    
    - id: "A02_CRYPTOGRAPHIC_FAILURES"
      name: "加密机制失效"
      severity: "HIGH"
      enabled: true
      checks:
        - type: "sast"
          patterns:
            - "weak encryption"
            - "hardcoded secrets"
```

#### 3.1.2 动态策略配置
```java
@RestController
@RequestMapping("/api/v1/policies")
public class PolicyController {
    
    @PostMapping("/{projectId}/apply")
    public ResponseEntity<PolicyResult> applyPolicy(
            @PathVariable Long projectId,
            @RequestBody PolicyConfig config) {
        
        // 验证策略配置
        PolicyValidator.validate(config);
        
        // 应用策略到项目
        policyService.applyToProject(projectId, config);
        
        // 触发重新扫描
        scanService.triggerRescan(projectId, config);
        
        return ResponseEntity.ok(new PolicyResult("Policy applied successfully"));
    }
}
```

### 3.2 合规性检查框架

#### 3.2.1 合规标准支持
- **OWASP Top 10**：Web应用安全风险
- **CWE Top 25**：最危险的软件错误
- **PCI DSS**：支付卡行业数据安全标准
- **SOX**：萨班斯-奥克斯利法案
- **GDPR**：通用数据保护条例
- **ISO 27001**：信息安全管理体系

#### 3.2.2 合规报告生成
```java
@Service
public class ComplianceReportService {
    
    public ComplianceReport generateReport(Long projectId, ComplianceStandard standard) {
        Project project = projectRepository.findById(projectId);
        List<Vulnerability> vulnerabilities = vulnerabilityRepository.findByProject(project);
        
        ComplianceReport report = new ComplianceReport();
        report.setProject(project);
        report.setStandard(standard);
        report.setGeneratedAt(LocalDateTime.now());
        
        // 按合规要求分类漏洞
        Map<String, List<Vulnerability>> categorized = categorizeByCompliance(vulnerabilities, standard);
        
        // 计算合规分数
        double complianceScore = calculateComplianceScore(categorized, standard);
        report.setComplianceScore(complianceScore);
        
        // 生成改进建议
        List<Recommendation> recommendations = generateRecommendations(categorized, standard);
        report.setRecommendations(recommendations);
        
        return report;
    }
}
```

## 4. 报告生成和仪表板

### 4.1 多维度报告系统

#### 4.1.1 报告类型
- **技术报告**：详细的漏洞技术分析
- **管理报告**：面向管理层的安全摘要
- **合规报告**：满足审计要求的合规性报告
- **趋势报告**：安全态势变化趋势分析

#### 4.1.2 报告模板引擎
```java
@Component
public class ReportTemplateEngine {
    
    private final Map<ReportType, ReportTemplate> templates;
    
    public Report generateReport(ReportRequest request) {
        ReportTemplate template = templates.get(request.getType());
        ReportContext context = buildContext(request);
        
        return template.render(context);
    }
    
    private ReportContext buildContext(ReportRequest request) {
        return ReportContext.builder()
            .project(request.getProject())
            .timeRange(request.getTimeRange())
            .vulnerabilities(getVulnerabilities(request))
            .metrics(calculateMetrics(request))
            .charts(generateCharts(request))
            .build();
    }
}
```

### 4.2 实时仪表板

#### 4.2.1 关键指标（KPI）
- **安全评分**：项目整体安全评分
- **漏洞统计**：按严重程度分类的漏洞数量
- **修复率**：漏洞修复完成率
- **平均修复时间**：MTTR (Mean Time To Repair)
- **新增漏洞趋势**：每日/周/月新增漏洞数量

#### 4.2.2 前端仪表板实现
```typescript
// React Dashboard组件
import React, { useEffect, useState } from 'react';
import { Card, Row, Col, Statistic, Progress } from 'antd';
import { Line, Pie, Column } from '@ant-design/plots';

const SecurityDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData>();

  useEffect(() => {
    fetchDashboardData().then(setDashboardData);
  }, []);

  const securityScoreConfig = {
    percent: dashboardData?.securityScore / 100,
    width: 120,
    height: 120,
    color: ['#5B8FF9', '#E8EDF3'],
    innerRadius: 0.64,
    radius: 0.98,
    statistic: {
      title: {
        style: {
          color: '#363636',
          fontSize: '12px',
        },
        content: '安全评分',
      },
      content: {
        style: {
          color: '#4B535E',
          fontSize: '24px',
          fontWeight: 'bold',
        },
        content: `${dashboardData?.securityScore}`,
      },
    },
  };

  return (
    <div className="dashboard-container">
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <Card title="安全评分">
            <Pie {...securityScoreConfig} />
          </Card>
        </Col>
        
        <Col span={6}>
          <Card title="漏洞统计">
            <Statistic
              title="严重漏洞"
              value={dashboardData?.criticalVulns}
              valueStyle={{ color: '#cf1322' }}
            />
            <Statistic
              title="高危漏洞"
              value={dashboardData?.highVulns}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        
        <Col span={12}>
          <Card title="漏洞趋势">
            <Line data={dashboardData?.trendData} {...trendConfig} />
          </Card>
        </Col>
      </Row>
    </div>
  );
};
```

## 5. CI/CD集成接口

### 5.1 Jenkins集成

#### 5.1.1 Jenkins插件开发
```groovy
// Jenkinsfile示例
pipeline {
    agent any
    
    stages {
        stage('Checkout') {
            steps {
                git 'https://github.com/example/project.git'
            }
        }
        
        stage('Build') {
            steps {
                sh 'mvn clean compile'
            }
        }
        
        stage('Security Scan') {
            steps {
                script {
                    // 调用SDL平台API进行安全扫描
                    def scanResult = sdlScan(
                        projectId: '123',
                        scanType: 'SAST,SCA',
                        branch: env.BRANCH_NAME
                    )
                    
                    // 检查扫描结果
                    if (scanResult.criticalVulns > 0) {
                        error("发现严重安全漏洞，构建失败")
                    }
                }
            }
        }
        
        stage('Deploy') {
            when {
                branch 'main'
            }
            steps {
                sh 'mvn deploy'
            }
        }
    }
    
    post {
        always {
            // 发布扫描报告
            publishHTML([
                allowMissing: false,
                alwaysLinkToLastBuild: true,
                keepAll: true,
                reportDir: 'security-reports',
                reportFiles: 'security-report.html',
                reportName: 'Security Scan Report'
            ])
        }
    }
}
```

### 5.2 GitLab CI集成

#### 5.2.1 GitLab CI配置
```yaml
# .gitlab-ci.yml
stages:
  - build
  - security-scan
  - deploy

variables:
  SDL_API_URL: "https://sdl-platform.company.com/api/v1"
  PROJECT_ID: "456"

security-scan:
  stage: security-scan
  image: alpine:latest
  before_script:
    - apk add --no-cache curl jq
  script:
    - |
      # 触发安全扫描
      SCAN_ID=$(curl -s -X POST \
        -H "Authorization: Bearer $SDL_API_TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
          \"projectId\": \"$PROJECT_ID\",
          \"branch\": \"$CI_COMMIT_REF_NAME\",
          \"commitId\": \"$CI_COMMIT_SHA\",
          \"scanTypes\": [\"SAST\", \"SCA\"]
        }" \
        "$SDL_API_URL/scans" | jq -r '.data.scanId')
      
      # 等待扫描完成
      while true; do
        STATUS=$(curl -s -H "Authorization: Bearer $SDL_API_TOKEN" \
          "$SDL_API_URL/scans/$SCAN_ID/status" | jq -r '.data.status')
        
        if [ "$STATUS" = "COMPLETED" ]; then
          break
        elif [ "$STATUS" = "FAILED" ]; then
          echo "扫描失败"
          exit 1
        fi
        
        sleep 30
      done
      
      # 检查扫描结果
      RESULT=$(curl -s -H "Authorization: Bearer $SDL_API_TOKEN" \
        "$SDL_API_URL/scans/$SCAN_ID/result")
      
      CRITICAL_COUNT=$(echo $RESULT | jq '.data.summary.critical')
      
      if [ "$CRITICAL_COUNT" -gt 0 ]; then
        echo "发现 $CRITICAL_COUNT 个严重漏洞，构建失败"
        exit 1
      fi
  artifacts:
    reports:
      junit: security-report.xml
    paths:
      - security-report.html
    expire_in: 1 week
```

## 6. 用户管理和权限控制

### 6.1 基于角色的访问控制（RBAC）

#### 6.1.1 权限模型设计
```java
@Entity
public class Permission {
    @Id
    private String code;
    private String name;
    private String resource;
    private String action;
    private String description;
}

@Entity
public class Role {
    @Id
    private Long id;
    private String name;
    private String description;
    
    @ManyToMany
    @JoinTable(name = "role_permissions")
    private Set<Permission> permissions;
}

@Entity
public class User {
    @Id
    private Long id;
    private String username;
    private String email;
    
    @ManyToMany
    @JoinTable(name = "user_roles")
    private Set<Role> roles;
}
```

#### 6.1.2 权限检查注解
```java
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequirePermission {
    String resource();
    String action();
}

// 使用示例
@RestController
public class VulnerabilityController {
    
    @GetMapping("/vulnerabilities")
    @RequirePermission(resource = "vulnerability", action = "read")
    public List<Vulnerability> getVulnerabilities() {
        return vulnerabilityService.findAll();
    }
    
    @PutMapping("/vulnerabilities/{id}/assign")
    @RequirePermission(resource = "vulnerability", action = "assign")
    public void assignVulnerability(@PathVariable Long id, @RequestBody AssignRequest request) {
        vulnerabilityService.assign(id, request.getAssigneeId());
    }
}
```
