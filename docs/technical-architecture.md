# SDL平台技术架构设计

## 1. 整体架构概览

SDL平台采用微服务架构，基于云原生技术栈构建，支持水平扩展和高可用部署。

### 1.1 架构层次

```
┌─────────────────────────────────────────────────────────────┐
│                    前端展示层 (Frontend)                      │
├─────────────────────────────────────────────────────────────┤
│                    API网关层 (API Gateway)                   │
├─────────────────────────────────────────────────────────────┤
│                    业务服务层 (Business Services)             │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层 (Data Access Layer)            │
├─────────────────────────────────────────────────────────────┤
│                    基础设施层 (Infrastructure)                │
└─────────────────────────────────────────────────────────────┘
```

## 2. 技术栈选择

### 2.1 前端技术栈

#### 2.1.1 核心框架
- **React 18.2+**：现代化的前端框架，支持并发特性
- **TypeScript 5.0+**：类型安全，提升开发效率和代码质量
- **Vite 4.0+**：快速的构建工具，支持热更新

#### 2.1.2 UI和组件
- **Ant Design Pro 6.0+**：企业级UI解决方案
- **Ant Design Charts**：基于G2Plot的图表组件
- **React Router 6**：前端路由管理
- **Styled Components**：CSS-in-JS样式解决方案

#### 2.1.3 状态管理
- **Redux Toolkit**：现代化的Redux状态管理
- **RTK Query**：数据获取和缓存解决方案
- **React Hook Form**：高性能表单处理

#### 2.1.4 开发工具
- **ESLint + Prettier**：代码规范和格式化
- **Husky + lint-staged**：Git钩子和代码检查
- **Jest + React Testing Library**：单元测试

### 2.2 后端技术栈

#### 2.2.1 核心框架
- **Spring Boot 3.1+**：基于Java 17的现代化框架
- **Spring Security 6**：安全认证和授权
- **Spring Data JPA**：数据访问层抽象
- **Spring Cloud Gateway**：API网关

#### 2.2.2 微服务组件
- **Nacos**：服务注册发现和配置管理
- **Sentinel**：流量控制和熔断降级
- **Seata**：分布式事务解决方案
- **OpenFeign**：声明式HTTP客户端

#### 2.2.3 数据存储
- **PostgreSQL 15+**：主数据库，支持JSON和全文搜索
- **Redis 7.0+**：缓存和会话存储
- **Elasticsearch 8.0+**：日志搜索和分析
- **MinIO**：对象存储，用于文件和报告存储

#### 2.2.4 消息和任务
- **Apache Kafka**：消息队列和事件流
- **XXL-Job**：分布式任务调度
- **RabbitMQ**：轻量级消息队列（可选）

### 2.3 DevOps技术栈

#### 2.3.1 容器化和编排
- **Docker**：应用容器化
- **Kubernetes**：容器编排和管理
- **Helm**：Kubernetes包管理

#### 2.3.2 CI/CD
- **GitLab CI**：持续集成和部署
- **ArgoCD**：GitOps部署管理
- **Harbor**：容器镜像仓库

#### 2.3.3 监控和日志
- **Prometheus**：指标监控
- **Grafana**：监控仪表板
- **Jaeger**：分布式链路追踪
- **ELK Stack**：日志收集和分析

## 3. 系统架构设计

### 3.1 微服务拆分

#### 3.1.1 用户管理服务 (User Service)
- **职责**：用户认证、授权、权限管理
- **技术栈**：Spring Boot + Spring Security + JWT
- **数据库**：PostgreSQL

#### 3.1.2 项目管理服务 (Project Service)
- **职责**：项目信息管理、配置管理
- **技术栈**：Spring Boot + Spring Data JPA
- **数据库**：PostgreSQL

#### 3.1.3 扫描引擎服务 (Scan Engine Service)
- **职责**：扫描任务调度、结果处理
- **技术栈**：Spring Boot + XXL-Job + Kafka
- **数据库**：PostgreSQL + Redis

#### 3.1.4 漏洞管理服务 (Vulnerability Service)
- **职责**：漏洞生命周期管理、风险评估
- **技术栈**：Spring Boot + Spring Data JPA
- **数据库**：PostgreSQL + Elasticsearch

#### 3.1.5 报告服务 (Report Service)
- **职责**：报告生成、数据分析
- **技术栈**：Spring Boot + Apache POI + ECharts
- **存储**：MinIO + PostgreSQL

#### 3.1.6 通知服务 (Notification Service)
- **职责**：消息通知、告警推送
- **技术栈**：Spring Boot + Kafka + WebSocket
- **数据库**：Redis

#### 3.1.7 集成服务 (Integration Service)
- **职责**：第三方工具集成、API适配
- **技术栈**：Spring Boot + OpenFeign
- **数据库**：PostgreSQL

### 3.2 数据架构设计

#### 3.2.1 数据库设计原则
- **微服务数据独立**：每个服务拥有独立的数据库
- **读写分离**：主从复制，读写分离提升性能
- **数据一致性**：使用Saga模式处理分布式事务

#### 3.2.2 核心数据模型

**用户和权限模型**：
```sql
-- 用户表
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 角色表
CREATE TABLE roles (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB
);

-- 用户角色关联表
CREATE TABLE user_roles (
    user_id BIGINT REFERENCES users(id),
    role_id BIGINT REFERENCES roles(id),
    PRIMARY KEY (user_id, role_id)
);
```

**项目和扫描模型**：
```sql
-- 项目表
CREATE TABLE projects (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    repository_url VARCHAR(500),
    branch VARCHAR(100) DEFAULT 'main',
    scan_config JSONB,
    created_by BIGINT REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 扫描任务表
CREATE TABLE scan_tasks (
    id BIGSERIAL PRIMARY KEY,
    project_id BIGINT REFERENCES projects(id),
    scan_type VARCHAR(20) NOT NULL, -- SAST, DAST, SCA, etc.
    status VARCHAR(20) DEFAULT 'PENDING',
    trigger_type VARCHAR(20), -- MANUAL, SCHEDULE, CI_CD
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    scan_config JSONB,
    result_summary JSONB
);
```

**漏洞管理模型**：
```sql
-- 漏洞表
CREATE TABLE vulnerabilities (
    id BIGSERIAL PRIMARY KEY,
    scan_task_id BIGINT REFERENCES scan_tasks(id),
    project_id BIGINT REFERENCES projects(id),
    vulnerability_id VARCHAR(100), -- 外部工具的漏洞ID
    title VARCHAR(200) NOT NULL,
    description TEXT,
    severity VARCHAR(20), -- CRITICAL, HIGH, MEDIUM, LOW
    cwe_id VARCHAR(20),
    file_path VARCHAR(500),
    line_number INTEGER,
    status VARCHAR(20) DEFAULT 'OPEN',
    assigned_to BIGINT REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3.3 API设计规范

#### 3.3.1 RESTful API设计
- **统一响应格式**：
```json
{
  "code": 200,
  "message": "Success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

- **错误处理**：
```json
{
  "code": 400,
  "message": "Validation failed",
  "errors": [
    {
      "field": "email",
      "message": "Invalid email format"
    }
  ],
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### 3.3.2 API版本管理
- **URL版本控制**：`/api/v1/projects`
- **向后兼容**：保持API向后兼容性
- **废弃策略**：提前通知API废弃计划

### 3.4 安全架构设计

#### 3.4.1 认证和授权
- **JWT Token**：无状态的身份认证
- **OAuth 2.0**：第三方登录集成
- **RBAC权限模型**：基于角色的访问控制

#### 3.4.2 数据安全
- **传输加密**：HTTPS/TLS 1.3
- **存储加密**：敏感数据AES-256加密
- **密钥管理**：使用Vault管理密钥

#### 3.4.3 API安全
- **限流控制**：基于用户和IP的限流
- **输入验证**：严格的输入参数验证
- **SQL注入防护**：使用参数化查询

## 4. 部署架构

### 4.1 容器化部署

#### 4.1.1 Docker镜像构建
- **多阶段构建**：减小镜像体积
- **基础镜像**：使用官方安全镜像
- **镜像扫描**：集成容器安全扫描

#### 4.1.2 Kubernetes部署
- **命名空间隔离**：不同环境使用不同命名空间
- **资源限制**：设置CPU和内存限制
- **健康检查**：配置存活性和就绪性探针

### 4.2 高可用设计

#### 4.2.1 服务高可用
- **多副本部署**：关键服务多副本部署
- **负载均衡**：使用Ingress Controller负载均衡
- **故障转移**：自动故障检测和转移

#### 4.2.2 数据高可用
- **数据库主从**：PostgreSQL主从复制
- **Redis集群**：Redis Cluster模式
- **数据备份**：定期数据备份和恢复测试

### 4.3 监控和运维

#### 4.3.1 应用监控
- **指标监控**：业务指标和技术指标
- **日志监控**：结构化日志和错误告警
- **链路追踪**：分布式请求链路追踪

#### 4.3.2 基础设施监控
- **资源监控**：CPU、内存、磁盘、网络
- **容器监控**：Pod状态和资源使用
- **集群监控**：Kubernetes集群健康状态

## 5. 性能优化策略

### 5.1 前端性能优化
- **代码分割**：按路由和组件分割代码
- **懒加载**：图片和组件懒加载
- **缓存策略**：浏览器缓存和CDN加速

### 5.2 后端性能优化
- **数据库优化**：索引优化和查询优化
- **缓存策略**：多级缓存架构
- **异步处理**：耗时操作异步处理

### 5.3 系统性能优化
- **连接池**：数据库和Redis连接池
- **批处理**：批量数据处理
- **压缩传输**：Gzip压缩减少传输量

## 6. 扩展性设计

### 6.1 水平扩展
- **无状态服务**：服务设计为无状态
- **数据分片**：大表数据分片存储
- **消息队列**：解耦服务间通信

### 6.2 插件化架构
- **扫描引擎插件**：支持新的扫描工具集成
- **通知插件**：支持新的通知渠道
- **报告插件**：支持自定义报告格式
