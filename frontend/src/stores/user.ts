import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginForm, LoginResponse } from '@/types'
import { authApi } from '@/api/auth'
import { getToken, setToken, removeToken } from '@/utils/auth'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string>('')
  const permissions = ref<string[]>([])

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const userRoles = computed(() => user.value?.roles || [])
  const userName = computed(() => user.value?.username || '')
  const userEmail = computed(() => user.value?.email || '')

  // 登录
  const login = async (loginForm: LoginForm): Promise<void> => {
    try {
      // 开发环境模拟登录
      if (import.meta.env.VITE_APP_ENV === 'development') {
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 简单的模拟验证
        if (loginForm.username === 'admin' && loginForm.password === 'admin123') {
          const mockToken = 'mock-jwt-token-' + Date.now()
          const mockUser = {
            id: 1,
            username: 'admin',
            email: '<EMAIL>',
            avatar: '',
            status: 'ACTIVE' as const,
            roles: [
              {
                id: 1,
                name: '系统管理员',
                description: '拥有系统所有权限',
                permissions: [
                  { id: 1, code: '*', name: '所有权限', resource: '*', action: '*', description: '系统所有权限' }
                ]
              }
            ],
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z'
          }

          // 保存token和用户信息
          token.value = mockToken
          user.value = mockUser
          setToken(mockToken)

          // 设置模拟权限
          permissions.value = ['*']

          return
        } else {
          throw new Error('用户名或密码错误')
        }
      }

      // 生产环境调用真实API
      const response = await authApi.login(loginForm)
      const { token: accessToken, user: userInfo } = response.data

      // 保存token和用户信息
      token.value = accessToken
      user.value = userInfo
      setToken(accessToken)

      // 获取用户权限
      await getUserPermissions()
    } catch (error) {
      throw error
    }
  }

  // 登出
  const logout = async (): Promise<void> => {
    try {
      await authApi.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // 清除本地状态
      token.value = ''
      user.value = null
      permissions.value = []
      removeToken()
    }
  }

  // 检查登录状态
  const checkLoginStatus = async (): Promise<void> => {
    const savedToken = getToken()
    if (!savedToken) {
      return
    }

    try {
      token.value = savedToken

      // 开发环境模拟用户信息
      if (import.meta.env.VITE_APP_ENV === 'development' && savedToken.startsWith('mock-jwt-token-')) {
        user.value = {
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          avatar: '',
          status: 'ACTIVE' as const,
          roles: [
            {
              id: 1,
              name: '系统管理员',
              description: '拥有系统所有权限',
              permissions: [
                { id: 1, code: '*', name: '所有权限', resource: '*', action: '*', description: '系统所有权限' }
              ]
            }
          ],
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }
        permissions.value = ['*']
        return
      }

      // 生产环境调用真实API
      const response = await authApi.getUserInfo()
      user.value = response.data
      await getUserPermissions()
    } catch (error) {
      // token无效，清除本地存储
      removeToken()
      token.value = ''
      user.value = null
      permissions.value = []
    }
  }

  // 获取用户权限
  const getUserPermissions = async (): Promise<void> => {
    try {
      const response = await authApi.getUserPermissions()
      permissions.value = response.data
    } catch (error) {
      console.error('Get permissions error:', error)
      permissions.value = []
    }
  }

  // 检查权限
  const hasPermission = (permission: string): boolean => {
    return permissions.value.includes(permission) || permissions.value.includes('*')
  }

  // 检查角色
  const hasRole = (roleName: string): boolean => {
    return userRoles.value.some(role => role.name === roleName)
  }

  // 更新用户信息
  const updateUserInfo = async (userInfo: Partial<User>): Promise<void> => {
    try {
      const response = await authApi.updateUserInfo(userInfo)
      user.value = response.data
    } catch (error) {
      throw error
    }
  }

  // 修改密码
  const changePassword = async (oldPassword: string, newPassword: string): Promise<void> => {
    try {
      await authApi.changePassword({ oldPassword, newPassword })
    } catch (error) {
      throw error
    }
  }

  return {
    // 状态
    user: readonly(user),
    token: readonly(token),
    permissions: readonly(permissions),
    
    // 计算属性
    isLoggedIn,
    userRoles,
    userName,
    userEmail,
    
    // 方法
    login,
    logout,
    checkLoginStatus,
    getUserPermissions,
    hasPermission,
    hasRole,
    updateUserInfo,
    changePassword
  }
})
