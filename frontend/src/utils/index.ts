import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'
import type { VulnerabilitySeverity, ScanStatus, VulnerabilityStatus } from '@/types'

// 配置dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

// 格式化日期
export function formatDate(date: string | Date, format = 'YYYY-MM-DD HH:mm:ss'): string {
  return dayjs(date).format(format)
}

// 格式化相对时间
export function formatRelativeTime(date: string | Date): string {
  return dayjs(date).fromNow()
}

// 格式化文件大小
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化数字
export function formatNumber(num: number): string {
  return num.toLocaleString()
}

// 获取漏洞严重程度颜色
export function getSeverityColor(severity: VulnerabilitySeverity): string {
  const colors = {
    CRITICAL: '#dc2626',
    HIGH: '#ea580c',
    MEDIUM: '#d97706',
    LOW: '#65a30d',
    INFO: '#0891b2'
  }
  return colors[severity] || '#6b7280'
}

// 获取漏洞严重程度标签
export function getSeverityLabel(severity: VulnerabilitySeverity): string {
  const labels = {
    CRITICAL: '严重',
    HIGH: '高危',
    MEDIUM: '中危',
    LOW: '低危',
    INFO: '信息'
  }
  return labels[severity] || severity
}

// 获取扫描状态颜色
export function getScanStatusColor(status: ScanStatus): string {
  const colors = {
    PENDING: '#6b7280',
    RUNNING: '#3b82f6',
    COMPLETED: '#10b981',
    FAILED: '#ef4444',
    CANCELLED: '#6b7280'
  }
  return colors[status] || '#6b7280'
}

// 获取扫描状态标签
export function getScanStatusLabel(status: ScanStatus): string {
  const labels = {
    PENDING: '等待中',
    RUNNING: '运行中',
    COMPLETED: '已完成',
    FAILED: '失败',
    CANCELLED: '已取消'
  }
  return labels[status] || status
}

// 获取扫描状态类型
export function getScanStatusType(status: ScanStatus): string {
  const types = {
    PENDING: 'info',
    RUNNING: 'primary',
    COMPLETED: 'success',
    FAILED: 'danger',
    CANCELLED: 'warning'
  }
  return types[status] || 'info'
}

// 获取漏洞状态颜色
export function getVulnStatusColor(status: VulnerabilityStatus): string {
  const colors = {
    OPEN: '#ef4444',
    CONFIRMED: '#f59e0b',
    IN_PROGRESS: '#3b82f6',
    RESOLVED: '#10b981',
    CLOSED: '#6b7280',
    FALSE_POSITIVE: '#8b5cf6'
  }
  return colors[status] || '#6b7280'
}

// 获取漏洞状态标签
export function getVulnStatusLabel(status: VulnerabilityStatus): string {
  const labels = {
    OPEN: '待处理',
    CONFIRMED: '已确认',
    IN_PROGRESS: '处理中',
    RESOLVED: '已解决',
    CLOSED: '已关闭',
    FALSE_POSITIVE: '误报'
  }
  return labels[status] || status
}

// 计算安全评分颜色
export function getSecurityScoreColor(score: number): string {
  if (score >= 90) return '#10b981'
  if (score >= 70) return '#65a30d'
  if (score >= 50) return '#d97706'
  if (score >= 30) return '#ea580c'
  return '#dc2626'
}

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let lastTime = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastTime >= wait) {
      lastTime = now
      func(...args)
    }
  }
}

// 深拷贝
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T
  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
  return obj
}

// 生成随机ID
export function generateId(): string {
  return Math.random().toString(36).substring(2, 11)
}

// 下载文件
export function downloadFile(url: string, filename?: string): void {
  const link = document.createElement('a')
  link.href = url
  if (filename) {
    link.download = filename
  }
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 复制到剪贴板
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text)
    return true
  } catch (error) {
    console.error('Copy to clipboard failed:', error)
    return false
  }
}
