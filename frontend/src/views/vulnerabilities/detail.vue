<template>
  <div class="vulnerability-detail-page">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>
    
    <div v-else-if="vulnerability" class="vulnerability-content">
      <!-- 漏洞基本信息 -->
      <el-card class="vuln-info-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <div class="vuln-title">
              <h2>{{ vulnerability.title }}</h2>
              <div class="vuln-tags">
                <el-tag
                  :type="getSeverityTagType(vulnerability.severity)"
                  size="large"
                >
                  {{ getSeverityLabel(vulnerability.severity) }}
                </el-tag>
                <el-tag
                  :type="getStatusTagType(vulnerability.status)"
                  size="large"
                >
                  {{ getStatusLabel(vulnerability.status) }}
                </el-tag>
              </div>
            </div>
            <div class="vuln-actions">
              <el-button
                type="primary"
                @click="handleAssign"
                v-if="userStore.hasPermission('vulnerability:assign')"
              >
                <el-icon><User /></el-icon>
                分配
              </el-button>
              <el-button
                type="success"
                @click="handleUpdateStatus"
                v-if="userStore.hasPermission('vulnerability:update')"
              >
                <el-icon><Edit /></el-icon>
                更新状态
              </el-button>
            </div>
          </div>
        </template>
        
        <div class="vuln-details">
          <el-row :gutter="24">
            <el-col :span="12">
              <div class="detail-item">
                <label>漏洞ID：</label>
                <span>{{ vulnerability.vulnerabilityId }}</span>
              </div>
              <div class="detail-item">
                <label>所属项目：</label>
                <el-link
                  @click="$router.push(`/projects/${vulnerability.projectId}`)"
                  type="primary"
                >
                  {{ vulnerability.projectName }}
                </el-link>
              </div>
              <div class="detail-item">
                <label>文件位置：</label>
                <span v-if="vulnerability.filePath">
                  {{ vulnerability.filePath }}:{{ vulnerability.lineNumber }}
                </span>
                <span v-else class="text-muted">未知</span>
              </div>
              <div class="detail-item">
                <label>CWE编号：</label>
                <span v-if="vulnerability.cweId">CWE-{{ vulnerability.cweId }}</span>
                <span v-else class="text-muted">未知</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>CVE编号：</label>
                <span v-if="vulnerability.cveId">{{ vulnerability.cveId }}</span>
                <span v-else class="text-muted">未知</span>
              </div>
              <div class="detail-item">
                <label>负责人：</label>
                <span v-if="vulnerability.assigneeName">{{ vulnerability.assigneeName }}</span>
                <span v-else class="text-muted">未分配</span>
              </div>
              <div class="detail-item">
                <label>发现时间：</label>
                <span>{{ formatDate(vulnerability.createdAt) }}</span>
              </div>
              <div class="detail-item">
                <label>更新时间：</label>
                <span>{{ formatDate(vulnerability.updatedAt) }}</span>
              </div>
            </el-col>
          </el-row>
          
          <div class="detail-item full-width">
            <label>漏洞描述：</label>
            <div class="vuln-description">
              {{ vulnerability.description || '暂无描述' }}
            </div>
          </div>
          
          <div class="detail-item full-width" v-if="vulnerability.tags.length > 0">
            <label>标签：</label>
            <div class="vuln-tags-list">
              <el-tag
                v-for="tag in vulnerability.tags"
                :key="tag"
                size="small"
                style="margin-right: 8px"
              >
                {{ tag }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 标签页内容 -->
      <el-tabs v-model="activeTab" class="vuln-tabs">
        <el-tab-pane label="修复建议" name="fix">
          <div class="tab-content">
            <div class="fix-suggestions">
              <h4>修复建议</h4>
              <p>根据漏洞类型和严重程度，建议采取以下修复措施：</p>
              <ul>
                <li>对用户输入进行严格的参数化查询处理</li>
                <li>使用预编译语句防止SQL注入攻击</li>
                <li>对特殊字符进行转义处理</li>
                <li>实施最小权限原则，限制数据库用户权限</li>
              </ul>
              
              <h4>参考资料</h4>
              <div class="reference-links">
                <el-link href="https://owasp.org/www-project-top-ten/" target="_blank" type="primary">
                  OWASP Top 10
                </el-link>
                <el-link href="https://cwe.mitre.org/" target="_blank" type="primary">
                  CWE详情
                </el-link>
              </div>
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="处理记录" name="history">
          <div class="tab-content">
            <div class="history-timeline">
              <el-timeline>
                <el-timeline-item
                  timestamp="2024-01-15 10:30"
                  type="primary"
                >
                  漏洞被发现并创建
                </el-timeline-item>
                <el-timeline-item
                  timestamp="2024-01-15 11:00"
                  type="warning"
                >
                  分配给张三处理
                </el-timeline-item>
                <el-timeline-item
                  timestamp="2024-01-15 14:30"
                  type="success"
                >
                  状态更新为处理中
                </el-timeline-item>
              </el-timeline>
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="评论" name="comments">
          <div class="tab-content">
            <div class="comments-section">
              <div class="add-comment">
                <el-input
                  v-model="newComment"
                  type="textarea"
                  :rows="3"
                  placeholder="添加评论..."
                />
                <div class="comment-actions">
                  <el-button type="primary" @click="handleAddComment">
                    添加评论
                  </el-button>
                </div>
              </div>
              
              <div class="comments-list">
                <div class="comment-item">
                  <div class="comment-header">
                    <span class="comment-author">张三</span>
                    <span class="comment-time">2024-01-15 14:30</span>
                  </div>
                  <div class="comment-content">
                    已经开始修复这个漏洞，预计明天完成。
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <div v-else class="error-container">
      <el-result
        icon="warning"
        title="漏洞不存在"
        sub-title="请检查漏洞ID是否正确"
      >
        <template #extra>
          <el-button type="primary" @click="$router.push('/vulnerabilities')">
            返回漏洞列表
          </el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { formatDate, getSeverityLabel } from '@/utils'
import type { Vulnerability } from '@/types'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 状态
const loading = ref(true)
const vulnerability = ref<Vulnerability | null>(null)
const activeTab = ref('fix')
const newComment = ref('')

// 获取严重程度标签类型
const getSeverityTagType = (severity: string) => {
  const types = {
    CRITICAL: 'danger',
    HIGH: 'warning',
    MEDIUM: '',
    LOW: 'success',
    INFO: 'info'
  }
  return types[severity as keyof typeof types] || ''
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const types = {
    OPEN: 'danger',
    CONFIRMED: 'warning',
    IN_PROGRESS: 'primary',
    RESOLVED: 'success',
    CLOSED: 'info',
    FALSE_POSITIVE: 'info'
  }
  return types[status as keyof typeof types] || ''
}

// 获取状态标签
const getStatusLabel = (status: string) => {
  const labels = {
    OPEN: '待处理',
    CONFIRMED: '已确认',
    IN_PROGRESS: '处理中',
    RESOLVED: '已解决',
    CLOSED: '已关闭',
    FALSE_POSITIVE: '误报'
  }
  return labels[status as keyof typeof labels] || status
}

// 获取漏洞详情
const fetchVulnerabilityDetail = async () => {
  try {
    loading.value = true
    const vulnId = Number(route.params.id)
    
    // 这里调用API获取漏洞详情
    // const response = await vulnerabilitiesApi.getVulnerability(vulnId)
    // vulnerability.value = response.data
    
    // 模拟数据
    vulnerability.value = {
      id: vulnId,
      scanTaskId: 1,
      projectId: 1,
      projectName: 'Web应用项目',
      vulnerabilityId: 'VULN-001',
      title: 'SQL注入漏洞',
      description: '在用户登录接口中发现SQL注入漏洞，攻击者可能通过构造恶意SQL语句获取敏感数据或执行未授权操作。该漏洞位于用户认证模块，影响范围较大，建议立即修复。',
      severity: 'HIGH',
      cweId: '89',
      cveId: 'CVE-2023-1234',
      filePath: 'src/controllers/auth.js',
      lineNumber: 45,
      status: 'IN_PROGRESS',
      assignedTo: 2,
      assigneeName: '张三',
      tags: ['SQL注入', '认证', '高危'],
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-15T14:30:00Z'
    }
    
  } catch (error) {
    ElMessage.error('获取漏洞详情失败')
    vulnerability.value = null
  } finally {
    loading.value = false
  }
}

// 分配漏洞
const handleAssign = () => {
  ElMessage.info('分配功能待实现')
}

// 更新状态
const handleUpdateStatus = () => {
  ElMessage.info('状态更新功能待实现')
}

// 添加评论
const handleAddComment = () => {
  if (!newComment.value.trim()) {
    ElMessage.warning('请输入评论内容')
    return
  }
  
  // 这里调用API添加评论
  // await vulnerabilitiesApi.addComment(vulnerability.value!.id, newComment.value)
  
  ElMessage.success('评论添加成功')
  newComment.value = ''
}

// 组件挂载时获取数据
onMounted(() => {
  fetchVulnerabilityDetail()
})
</script>

<style scoped>
.vulnerability-detail-page {
  padding: 0;
}

.loading-container {
  padding: 24px;
}

.vulnerability-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.vuln-info-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.vuln-title {
  flex: 1;
}

.vuln-title h2 {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
}

.vuln-tags {
  display: flex;
  gap: 8px;
}

.vuln-actions {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

.vuln-details {
  padding: 16px 0;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  gap: 8px;
}

.detail-item.full-width {
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.detail-item label {
  min-width: 80px;
  font-weight: 500;
  color: #666;
  flex-shrink: 0;
}

.detail-item span {
  color: #333;
}

.vuln-description {
  color: #333;
  line-height: 1.6;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.vuln-tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.text-muted {
  color: #999;
}

.vuln-tabs {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-content {
  padding: 24px 0;
  min-height: 300px;
}

.fix-suggestions h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
}

.fix-suggestions p {
  margin: 0 0 16px 0;
  color: #666;
  line-height: 1.6;
}

.fix-suggestions ul {
  margin: 0 0 24px 0;
  padding-left: 20px;
}

.fix-suggestions li {
  margin-bottom: 8px;
  color: #666;
  line-height: 1.6;
}

.reference-links {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.history-timeline {
  max-width: 600px;
}

.comments-section {
  max-width: 800px;
}

.add-comment {
  margin-bottom: 24px;
}

.comment-actions {
  margin-top: 12px;
  text-align: right;
}

.comments-list {
  border-top: 1px solid #ebeef5;
  padding-top: 24px;
}

.comment-item {
  margin-bottom: 20px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 8px;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.comment-author {
  font-weight: 500;
  color: #333;
}

.comment-time {
  font-size: 12px;
  color: #999;
}

.comment-content {
  color: #666;
  line-height: 1.6;
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .vuln-actions {
    justify-content: center;
  }
  
  .detail-item {
    flex-direction: column;
    gap: 4px;
  }
  
  .detail-item label {
    min-width: auto;
  }
}
</style>
