<template>
  <div class="vulnerabilities-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>漏洞管理</h2>
      <p>管理和跟踪安全漏洞的生命周期</p>
    </div>

    <!-- 搜索和操作栏 -->
    <div class="table-container">
      <div class="table-header">
        <div class="table-search">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索漏洞标题或描述"
            style="width: 240px"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <el-select
            v-model="searchForm.projectId"
            placeholder="选择项目"
            style="width: 200px"
            clearable
            filterable
          >
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
          
          <el-select
            v-model="searchForm.severity"
            placeholder="严重程度"
            style="width: 120px"
            clearable
          >
            <el-option label="严重" value="CRITICAL" />
            <el-option label="高危" value="HIGH" />
            <el-option label="中危" value="MEDIUM" />
            <el-option label="低危" value="LOW" />
            <el-option label="信息" value="INFO" />
          </el-select>
          
          <el-select
            v-model="searchForm.status"
            placeholder="处理状态"
            style="width: 120px"
            clearable
          >
            <el-option label="待处理" value="OPEN" />
            <el-option label="已确认" value="CONFIRMED" />
            <el-option label="处理中" value="IN_PROGRESS" />
            <el-option label="已解决" value="RESOLVED" />
            <el-option label="已关闭" value="CLOSED" />
            <el-option label="误报" value="FALSE_POSITIVE" />
          </el-select>
          
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>
        
        <div class="table-actions">
          <el-button
            type="success"
            :disabled="selectedVulns.length === 0"
            @click="handleBatchAssign"
            v-if="userStore.hasPermission('vulnerability:assign')"
          >
            <el-icon><User /></el-icon>
            批量分配
          </el-button>
          
          <el-button
            type="warning"
            :disabled="selectedVulns.length === 0"
            @click="handleBatchStatus"
            v-if="userStore.hasPermission('vulnerability:update')"
          >
            <el-icon><Edit /></el-icon>
            批量处理
          </el-button>
        </div>
      </div>

      <!-- 漏洞表格 -->
      <el-table
        v-loading="loading"
        :data="vulnerabilities"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column label="漏洞信息" min-width="300">
          <template #default="{ row }">
            <div class="vuln-info">
              <div class="vuln-title">
                <el-link
                  type="primary"
                  @click="handleViewDetail(row)"
                  :underline="false"
                >
                  {{ row.title }}
                </el-link>
              </div>
              <div class="vuln-desc">{{ row.description || '暂无描述' }}</div>
              <div class="vuln-location" v-if="row.filePath">
                <el-icon><Document /></el-icon>
                <span>{{ row.filePath }}:{{ row.lineNumber }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="项目" width="150">
          <template #default="{ row }">
            <el-link
              @click="$router.push(`/projects/${row.projectId}`)"
              type="primary"
              :underline="false"
            >
              {{ row.projectName }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column label="严重程度" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getSeverityTagType(row.severity)"
              size="small"
            >
              {{ getSeverityLabel(row.severity) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getStatusTagType(row.status)"
              size="small"
            >
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="负责人" width="120">
          <template #default="{ row }">
            <span v-if="row.assigneeName">{{ row.assigneeName }}</span>
            <span v-else class="text-muted">未分配</span>
          </template>
        </el-table-column>
        
        <el-table-column label="CWE/CVE" width="120">
          <template #default="{ row }">
            <div class="cwe-cve">
              <div v-if="row.cweId" class="cwe-id">CWE-{{ row.cweId }}</div>
              <div v-if="row.cveId" class="cve-id">{{ row.cveId }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column
          prop="createdAt"
          label="发现时间"
          width="180"
          :formatter="(row) => formatDate(row.createdAt)"
        />
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>
            
            <el-dropdown
              @command="(command) => handleDropdownCommand(command, row)"
              trigger="click"
            >
              <el-button size="small">
                更多
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    command="assign"
                    v-if="userStore.hasPermission('vulnerability:assign')"
                  >
                    分配
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="status"
                    v-if="userStore.hasPermission('vulnerability:update')"
                  >
                    更新状态
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="false_positive"
                    v-if="userStore.hasPermission('vulnerability:update')"
                  >
                    标记误报
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="table-pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { formatDate, getSeverityLabel } from '@/utils'
import type { Vulnerability, Project } from '@/types'

const router = useRouter()
const userStore = useUserStore()

// 状态
const loading = ref(false)
const vulnerabilities = ref<Vulnerability[]>([])
const projects = ref<Project[]>([])
const selectedVulns = ref<Vulnerability[]>([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  projectId: undefined as number | undefined,
  severity: '',
  status: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 获取严重程度标签类型
const getSeverityTagType = (severity: string) => {
  const types = {
    CRITICAL: 'danger',
    HIGH: 'warning',
    MEDIUM: '',
    LOW: 'success',
    INFO: 'info'
  }
  return types[severity as keyof typeof types] || ''
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const types = {
    OPEN: 'danger',
    CONFIRMED: 'warning',
    IN_PROGRESS: 'primary',
    RESOLVED: 'success',
    CLOSED: 'info',
    FALSE_POSITIVE: 'info'
  }
  return types[status as keyof typeof types] || ''
}

// 获取状态标签
const getStatusLabel = (status: string) => {
  const labels = {
    OPEN: '待处理',
    CONFIRMED: '已确认',
    IN_PROGRESS: '处理中',
    RESOLVED: '已解决',
    CLOSED: '已关闭',
    FALSE_POSITIVE: '误报'
  }
  return labels[status as keyof typeof labels] || status
}

// 获取漏洞列表
const fetchVulnerabilities = async () => {
  try {
    loading.value = true
    
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    
    // 这里调用API获取漏洞列表
    // const response = await vulnerabilitiesApi.getVulnerabilities(params)
    // vulnerabilities.value = response.data.list
    // pagination.total = response.data.total
    
    // 模拟数据
    vulnerabilities.value = [
      {
        id: 1,
        scanTaskId: 1,
        projectId: 1,
        projectName: 'Web应用项目',
        vulnerabilityId: 'VULN-001',
        title: 'SQL注入漏洞',
        description: '在用户登录接口中发现SQL注入漏洞',
        severity: 'HIGH',
        cweId: '89',
        cveId: 'CVE-2023-1234',
        filePath: 'src/controllers/auth.js',
        lineNumber: 45,
        status: 'OPEN',
        assignedTo: 2,
        assigneeName: '张三',
        tags: ['SQL注入', '认证'],
        createdAt: '2024-01-15T10:30:00Z',
        updatedAt: '2024-01-15T10:30:00Z'
      },
      {
        id: 2,
        scanTaskId: 1,
        projectId: 1,
        projectName: 'Web应用项目',
        vulnerabilityId: 'VULN-002',
        title: 'XSS跨站脚本攻击',
        description: '用户输入未经过滤直接输出到页面',
        severity: 'MEDIUM',
        cweId: '79',
        filePath: 'src/views/profile.vue',
        lineNumber: 123,
        status: 'IN_PROGRESS',
        assignedTo: 3,
        assigneeName: '李四',
        tags: ['XSS', '输入验证'],
        createdAt: '2024-01-15T10:30:00Z',
        updatedAt: '2024-01-15T11:00:00Z'
      }
    ]
    pagination.total = 2
  } catch (error) {
    ElMessage.error('获取漏洞列表失败')
  } finally {
    loading.value = false
  }
}

// 获取项目列表
const fetchProjects = async () => {
  try {
    // 这里调用API获取项目列表
    // const response = await projectsApi.getProjects()
    // projects.value = response.data.list
    
    // 模拟数据
    projects.value = [
      { id: 1, name: 'Web应用项目' },
      { id: 2, name: 'API服务项目' },
      { id: 3, name: '移动应用项目' }
    ] as Project[]
  } catch (error) {
    console.error('获取项目列表失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchVulnerabilities()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    projectId: undefined,
    severity: '',
    status: ''
  })
  pagination.page = 1
  fetchVulnerabilities()
}

// 选择变化
const handleSelectionChange = (selection: Vulnerability[]) => {
  selectedVulns.value = selection
}

// 查看详情
const handleViewDetail = (vuln: Vulnerability) => {
  router.push(`/vulnerabilities/${vuln.id}`)
}

// 下拉菜单命令处理
const handleDropdownCommand = (command: string, vuln: Vulnerability) => {
  switch (command) {
    case 'assign':
      handleAssign(vuln)
      break
    case 'status':
      handleUpdateStatus(vuln)
      break
    case 'false_positive':
      handleMarkFalsePositive(vuln)
      break
  }
}

// 分配漏洞
const handleAssign = (vuln: Vulnerability) => {
  ElMessage.info('分配功能待实现')
}

// 更新状态
const handleUpdateStatus = (vuln: Vulnerability) => {
  ElMessage.info('状态更新功能待实现')
}

// 标记误报
const handleMarkFalsePositive = (vuln: Vulnerability) => {
  ElMessage.info('误报标记功能待实现')
}

// 批量分配
const handleBatchAssign = () => {
  ElMessage.info('批量分配功能待实现')
}

// 批量处理状态
const handleBatchStatus = () => {
  ElMessage.info('批量状态处理功能待实现')
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  fetchVulnerabilities()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchVulnerabilities()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchVulnerabilities()
  fetchProjects()
})
</script>

<style scoped>
.vulnerabilities-page {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.table-container {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 20px;
}

.table-search {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.table-actions {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

.vuln-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.vuln-title {
  font-weight: 500;
  font-size: 14px;
}

.vuln-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.vuln-location {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #999;
}

.cwe-cve {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.cwe-id,
.cve-id {
  font-size: 12px;
  color: #666;
}

.text-muted {
  color: #999;
  font-size: 12px;
}

.table-pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .table-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .table-search {
    flex-direction: column;
    align-items: stretch;
  }
  
  .table-actions {
    justify-content: flex-start;
  }
}
</style>
