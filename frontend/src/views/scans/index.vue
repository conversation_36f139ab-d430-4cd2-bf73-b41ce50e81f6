<template>
  <div class="scans-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>扫描任务</h2>
      <p>管理和监控安全扫描任务的执行状态</p>
    </div>

    <!-- 搜索和操作栏 -->
    <div class="table-container">
      <div class="table-header">
        <div class="table-search">
          <el-select
            v-model="searchForm.projectId"
            placeholder="选择项目"
            style="width: 200px"
            clearable
            filterable
          >
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
          
          <el-select
            v-model="searchForm.status"
            placeholder="扫描状态"
            style="width: 120px"
            clearable
          >
            <el-option label="等待中" value="PENDING" />
            <el-option label="运行中" value="RUNNING" />
            <el-option label="已完成" value="COMPLETED" />
            <el-option label="失败" value="FAILED" />
            <el-option label="已取消" value="CANCELLED" />
          </el-select>
          
          <el-select
            v-model="searchForm.scanType"
            placeholder="扫描类型"
            style="width: 140px"
            clearable
          >
            <el-option label="静态扫描" value="SAST" />
            <el-option label="动态扫描" value="DAST" />
            <el-option label="组件扫描" value="SCA" />
            <el-option label="容器扫描" value="CONTAINER" />
            <el-option label="基础设施扫描" value="IAC" />
          </el-select>
          
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
            clearable
          />
          
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>
        
        <div class="table-actions">
          <el-button
            type="primary"
            @click="handleCreate"
            v-if="userStore.hasPermission('scan:create')"
          >
            <el-icon><Plus /></el-icon>
            新建扫描
          </el-button>
        </div>
      </div>

      <!-- 扫描任务表格 -->
      <el-table
        v-loading="loading"
        :data="scans"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="任务ID" width="80" />
        
        <el-table-column label="项目信息" min-width="200">
          <template #default="{ row }">
            <div class="project-info">
              <div class="project-name">{{ row.projectName }}</div>
              <div class="scan-branch">分支: {{ row.branch }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="扫描类型" width="150">
          <template #default="{ row }">
            <el-tag
              v-for="type in row.scanType"
              :key="type"
              size="small"
              style="margin-right: 4px; margin-bottom: 4px"
            >
              {{ getScanTypeLabel(type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getScanStatusType(row.status)"
              size="small"
            >
              {{ getScanStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="触发方式" width="100">
          <template #default="{ row }">
            <span>{{ getTriggerTypeLabel(row.triggerType) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="扫描结果" width="150">
          <template #default="{ row }">
            <div v-if="row.resultSummary" class="scan-result">
              <el-tag
                v-if="row.resultSummary.critical > 0"
                type="danger"
                size="small"
              >
                严重: {{ row.resultSummary.critical }}
              </el-tag>
              <el-tag
                v-if="row.resultSummary.high > 0"
                type="warning"
                size="small"
              >
                高危: {{ row.resultSummary.high }}
              </el-tag>
              <span v-if="row.resultSummary.total === 0" class="no-issues">
                无问题
              </span>
            </div>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="耗时" width="100">
          <template #default="{ row }">
            <span v-if="row.duration">{{ formatDuration(row.duration) }}</span>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        
        <el-table-column
          prop="createdAt"
          label="创建时间"
          width="180"
          :formatter="(row) => formatDate(row.createdAt)"
        />
        
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>
            
            <el-button
              v-if="row.status === 'RUNNING'"
              type="warning"
              size="small"
              @click="handleCancel(row)"
              v-show="userStore.hasPermission('scan:cancel')"
            >
              取消
            </el-button>
            
            <el-button
              v-if="['COMPLETED', 'FAILED'].includes(row.status)"
              type="success"
              size="small"
              @click="handleRerun(row)"
              v-show="userStore.hasPermission('scan:create')"
            >
              重跑
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="table-pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { formatDate, getScanStatusLabel } from '@/utils'
import type { ScanTask, Project } from '@/types'

const router = useRouter()
const userStore = useUserStore()

// 状态
const loading = ref(false)
const scans = ref<ScanTask[]>([])
const projects = ref<Project[]>([])
const dateRange = ref<[Date, Date] | null>(null)

// 搜索表单
const searchForm = reactive({
  projectId: undefined as number | undefined,
  status: '',
  scanType: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 获取扫描类型标签
const getScanTypeLabel = (type: string) => {
  const labels = {
    SAST: '静态',
    DAST: '动态',
    SCA: '组件',
    CONTAINER: '容器',
    IAC: '基础设施'
  }
  return labels[type as keyof typeof labels] || type
}

// 获取扫描状态类型
const getScanStatusType = (status: string) => {
  const types = {
    PENDING: 'info',
    RUNNING: 'primary',
    COMPLETED: 'success',
    FAILED: 'danger',
    CANCELLED: 'warning'
  }
  return types[status as keyof typeof types] || 'info'
}

// 获取触发方式标签
const getTriggerTypeLabel = (type: string) => {
  const labels = {
    MANUAL: '手动',
    SCHEDULE: '定时',
    CI_CD: 'CI/CD',
    WEBHOOK: 'Webhook'
  }
  return labels[type as keyof typeof labels] || type
}

// 格式化持续时间
const formatDuration = (seconds: number) => {
  if (seconds < 60) return `${seconds}秒`
  if (seconds < 3600) return `${Math.floor(seconds / 60)}分钟`
  return `${Math.floor(seconds / 3600)}小时`
}

// 获取扫描任务列表
const fetchScans = async () => {
  try {
    loading.value = true
    
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    
    if (dateRange.value) {
      params.startDate = dateRange.value[0].toISOString()
      params.endDate = dateRange.value[1].toISOString()
    }
    
    // 这里调用API获取扫描任务列表
    // const response = await scansApi.getScans(params)
    // scans.value = response.data.list
    // pagination.total = response.data.total
    
    // 模拟数据
    scans.value = [
      {
        id: 1,
        projectId: 1,
        projectName: 'Web应用项目',
        scanType: ['SAST', 'SCA'],
        status: 'COMPLETED',
        triggerType: 'MANUAL',
        branch: 'main',
        duration: 180,
        resultSummary: {
          critical: 0,
          high: 2,
          medium: 8,
          low: 15,
          info: 5,
          total: 30,
          securityScore: 85
        },
        createdBy: 1,
        createdAt: '2024-01-15T10:30:00Z'
      },
      {
        id: 2,
        projectId: 2,
        projectName: 'API服务项目',
        scanType: ['SAST', 'DAST', 'SCA'],
        status: 'RUNNING',
        triggerType: 'CI_CD',
        branch: 'main',
        createdBy: 1,
        createdAt: '2024-01-15T09:15:00Z'
      }
    ]
    pagination.total = 2
  } catch (error) {
    ElMessage.error('获取扫描任务列表失败')
  } finally {
    loading.value = false
  }
}

// 获取项目列表
const fetchProjects = async () => {
  try {
    // 这里调用API获取项目列表
    // const response = await projectsApi.getProjects()
    // projects.value = response.data.list
    
    // 模拟数据
    projects.value = [
      { id: 1, name: 'Web应用项目' },
      { id: 2, name: 'API服务项目' },
      { id: 3, name: '移动应用项目' }
    ] as Project[]
  } catch (error) {
    console.error('获取项目列表失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchScans()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    projectId: undefined,
    status: '',
    scanType: ''
  })
  dateRange.value = null
  pagination.page = 1
  fetchScans()
}

// 查看详情
const handleViewDetail = (scan: ScanTask) => {
  router.push(`/scans/${scan.id}`)
}

// 新建扫描
const handleCreate = () => {
  router.push('/scans/create')
}

// 取消扫描
const handleCancel = async (scan: ScanTask) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消扫描任务 "${scan.id}" 吗？`,
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里调用API取消扫描
    // await scansApi.cancelScan(scan.id)
    
    scan.status = 'CANCELLED'
    ElMessage.success('扫描任务已取消')
  } catch (error) {
    // 用户取消操作
  }
}

// 重新运行扫描
const handleRerun = async (scan: ScanTask) => {
  try {
    await ElMessageBox.confirm(
      `确定要重新运行扫描任务 "${scan.id}" 吗？`,
      '确认重跑',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    // 这里调用API重新运行扫描
    // await scansApi.rerunScan(scan.id)
    
    ElMessage.success('扫描任务已重新启动')
    fetchScans()
  } catch (error) {
    // 用户取消操作
  }
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  fetchScans()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchScans()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchScans()
  fetchProjects()
})
</script>

<style scoped>
.scans-page {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.table-container {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 20px;
}

.table-search {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.table-actions {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

.project-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.project-name {
  font-weight: 500;
  color: #333;
}

.scan-branch {
  font-size: 12px;
  color: #666;
}

.scan-result {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.no-issues {
  color: #67c23a;
  font-size: 12px;
}

.text-muted {
  color: #999;
  font-size: 12px;
}

.table-pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .table-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .table-search {
    flex-direction: column;
    align-items: stretch;
  }
  
  .table-actions {
    justify-content: flex-start;
  }
}
</style>
