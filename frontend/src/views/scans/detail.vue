<template>
  <div class="scan-detail-page">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>
    
    <div v-else-if="scan" class="scan-content">
      <!-- 扫描任务基本信息 -->
      <el-card class="scan-info-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <div class="scan-title">
              <h2>扫描任务 #{{ scan.id }}</h2>
              <el-tag
                :type="getScanStatusType(scan.status)"
                size="large"
              >
                {{ getScanStatusLabel(scan.status) }}
              </el-tag>
            </div>
            <div class="scan-actions">
              <el-button
                v-if="scan.status === 'RUNNING'"
                type="warning"
                @click="handleCancel"
              >
                <el-icon><Close /></el-icon>
                取消扫描
              </el-button>
              <el-button
                v-if="['COMPLETED', 'FAILED'].includes(scan.status)"
                type="success"
                @click="handleRerun"
              >
                <el-icon><Refresh /></el-icon>
                重新扫描
              </el-button>
            </div>
          </div>
        </template>
        
        <div class="scan-details">
          <el-row :gutter="24">
            <el-col :span="12">
              <div class="detail-item">
                <label>项目名称：</label>
                <el-link @click="$router.push(`/projects/${scan.projectId}`)" type="primary">
                  {{ scan.projectName }}
                </el-link>
              </div>
              <div class="detail-item">
                <label>扫描分支：</label>
                <span>{{ scan.branch }}</span>
              </div>
              <div class="detail-item">
                <label>扫描类型：</label>
                <div class="scan-types">
                  <el-tag
                    v-for="type in scan.scanType"
                    :key="type"
                    size="small"
                    style="margin-right: 8px"
                  >
                    {{ getScanTypeLabel(type) }}
                  </el-tag>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>触发方式：</label>
                <span>{{ getTriggerTypeLabel(scan.triggerType) }}</span>
              </div>
              <div class="detail-item">
                <label>创建时间：</label>
                <span>{{ formatDate(scan.createdAt) }}</span>
              </div>
              <div class="detail-item" v-if="scan.duration">
                <label>扫描耗时：</label>
                <span>{{ formatDuration(scan.duration) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 扫描结果概览 -->
      <el-card v-if="scan.resultSummary" class="result-summary-card" shadow="hover">
        <template #header>
          <span>扫描结果概览</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="4">
            <div class="summary-item critical">
              <div class="summary-value">{{ scan.resultSummary.critical }}</div>
              <div class="summary-label">严重</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="summary-item high">
              <div class="summary-value">{{ scan.resultSummary.high }}</div>
              <div class="summary-label">高危</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="summary-item medium">
              <div class="summary-value">{{ scan.resultSummary.medium }}</div>
              <div class="summary-label">中危</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="summary-item low">
              <div class="summary-value">{{ scan.resultSummary.low }}</div>
              <div class="summary-label">低危</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="summary-item info">
              <div class="summary-value">{{ scan.resultSummary.info }}</div>
              <div class="summary-label">信息</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="summary-item total">
              <div class="summary-value">{{ scan.resultSummary.total }}</div>
              <div class="summary-label">总计</div>
            </div>
          </el-col>
        </el-row>
        
        <div class="security-score">
          <label>安全评分：</label>
          <el-progress
            :percentage="scan.resultSummary.securityScore"
            :color="getSecurityScoreColor(scan.resultSummary.securityScore)"
            :stroke-width="12"
            text-inside
            style="width: 300px"
          />
        </div>
      </el-card>

      <!-- 标签页内容 -->
      <el-tabs v-model="activeTab" class="scan-tabs">
        <el-tab-pane label="漏洞详情" name="vulnerabilities">
          <div class="tab-content">
            <p>漏洞详情内容（待实现）</p>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="扫描日志" name="logs">
          <div class="tab-content">
            <p>扫描日志内容（待实现）</p>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="报告下载" name="reports">
          <div class="tab-content">
            <div class="report-downloads">
              <el-button type="primary" @click="downloadReport('pdf')">
                <el-icon><Download /></el-icon>
                下载PDF报告
              </el-button>
              <el-button @click="downloadReport('excel')">
                <el-icon><Download /></el-icon>
                下载Excel报告
              </el-button>
              <el-button @click="downloadReport('json')">
                <el-icon><Download /></el-icon>
                下载JSON数据
              </el-button>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <div v-else class="error-container">
      <el-result
        icon="warning"
        title="扫描任务不存在"
        sub-title="请检查任务ID是否正确"
      >
        <template #extra>
          <el-button type="primary" @click="$router.push('/scans')">
            返回扫描列表
          </el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDate, getScanStatusLabel, getSecurityScoreColor } from '@/utils'
import type { ScanTask } from '@/types'

const route = useRoute()
const router = useRouter()

// 状态
const loading = ref(true)
const scan = ref<ScanTask | null>(null)
const activeTab = ref('vulnerabilities')

// 获取扫描类型标签
const getScanTypeLabel = (type: string) => {
  const labels = {
    SAST: '静态代码扫描',
    DAST: '动态应用扫描',
    SCA: '组件安全扫描',
    CONTAINER: '容器安全扫描',
    IAC: '基础设施扫描'
  }
  return labels[type as keyof typeof labels] || type
}

// 获取扫描状态类型
const getScanStatusType = (status: string) => {
  const types = {
    PENDING: 'info',
    RUNNING: 'primary',
    COMPLETED: 'success',
    FAILED: 'danger',
    CANCELLED: 'warning'
  }
  return types[status as keyof typeof types] || 'info'
}

// 获取触发方式标签
const getTriggerTypeLabel = (type: string) => {
  const labels = {
    MANUAL: '手动触发',
    SCHEDULE: '定时触发',
    CI_CD: 'CI/CD触发',
    WEBHOOK: 'Webhook触发'
  }
  return labels[type as keyof typeof labels] || type
}

// 格式化持续时间
const formatDuration = (seconds: number) => {
  if (seconds < 60) return `${seconds}秒`
  if (seconds < 3600) return `${Math.floor(seconds / 60)}分${seconds % 60}秒`
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  return `${hours}小时${minutes}分钟`
}

// 获取扫描详情
const fetchScanDetail = async () => {
  try {
    loading.value = true
    const scanId = Number(route.params.id)
    
    // 这里调用API获取扫描详情
    // const response = await scansApi.getScan(scanId)
    // scan.value = response.data
    
    // 模拟数据
    scan.value = {
      id: scanId,
      projectId: 1,
      projectName: 'Web应用项目',
      scanType: ['SAST', 'SCA'],
      status: 'COMPLETED',
      triggerType: 'MANUAL',
      branch: 'main',
      commitId: 'abc123def456',
      startedAt: '2024-01-15T10:30:00Z',
      completedAt: '2024-01-15T10:33:00Z',
      duration: 180,
      resultSummary: {
        critical: 0,
        high: 2,
        medium: 8,
        low: 15,
        info: 5,
        total: 30,
        securityScore: 85
      },
      createdBy: 1,
      createdAt: '2024-01-15T10:30:00Z'
    }
    
  } catch (error) {
    ElMessage.error('获取扫描详情失败')
    scan.value = null
  } finally {
    loading.value = false
  }
}

// 取消扫描
const handleCancel = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要取消当前扫描任务吗？',
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里调用API取消扫描
    // await scansApi.cancelScan(scan.value!.id)
    
    scan.value!.status = 'CANCELLED'
    ElMessage.success('扫描任务已取消')
  } catch (error) {
    // 用户取消操作
  }
}

// 重新扫描
const handleRerun = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重新运行此扫描任务吗？',
      '确认重跑',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    // 这里调用API重新运行扫描
    // const response = await scansApi.rerunScan(scan.value!.id)
    // const newScanId = response.data.id
    
    ElMessage.success('扫描任务已重新启动')
    // router.push(`/scans/${newScanId}`)
  } catch (error) {
    // 用户取消操作
  }
}

// 下载报告
const downloadReport = async (format: string) => {
  try {
    ElMessage.info('正在生成报告，请稍候...')
    
    // 这里调用API下载报告
    // const response = await scansApi.downloadReport(scan.value!.id, format)
    // 处理文件下载
    
    ElMessage.success('报告下载成功')
  } catch (error) {
    ElMessage.error('报告下载失败')
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchScanDetail()
})
</script>

<style scoped>
.scan-detail-page {
  padding: 0;
}

.loading-container {
  padding: 24px;
}

.scan-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.scan-info-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.scan-title {
  display: flex;
  align-items: center;
  gap: 16px;
}

.scan-title h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.scan-actions {
  display: flex;
  gap: 12px;
}

.scan-details {
  padding: 16px 0;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  gap: 8px;
}

.detail-item label {
  min-width: 80px;
  font-weight: 500;
  color: #666;
}

.detail-item span {
  color: #333;
}

.scan-types {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.result-summary-card {
  margin-bottom: 24px;
}

.summary-item {
  text-align: center;
  padding: 16px;
  border-radius: 8px;
  background: #f5f7fa;
}

.summary-item.critical {
  background: #fef0f0;
  color: #f56c6c;
}

.summary-item.high {
  background: #fdf6ec;
  color: #e6a23c;
}

.summary-item.medium {
  background: #fefce8;
  color: #f7ba2a;
}

.summary-item.low {
  background: #f0f9ff;
  color: #67c23a;
}

.summary-item.info {
  background: #f0f9ff;
  color: #409eff;
}

.summary-item.total {
  background: #f4f4f5;
  color: #909399;
}

.summary-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.summary-label {
  font-size: 14px;
}

.security-score {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #ebeef5;
}

.security-score label {
  font-weight: 500;
  color: #666;
}

.scan-tabs {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-content {
  padding: 24px 0;
  min-height: 300px;
}

.report-downloads {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .scan-actions {
    justify-content: center;
  }
  
  .detail-item {
    flex-direction: column;
    gap: 4px;
  }
  
  .detail-item label {
    min-width: auto;
  }
  
  .security-score {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .report-downloads {
    flex-direction: column;
  }
}
</style>
