<template>
  <div class="reports-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>安全报告</h2>
      <p>生成和管理各类安全分析报告</p>
    </div>

    <!-- 报告生成区域 -->
    <el-card class="report-generator" shadow="hover">
      <template #header>
        <span>生成新报告</span>
      </template>
      
      <el-form
        ref="reportFormRef"
        :model="reportForm"
        :rules="reportRules"
        label-width="120px"
        inline
      >
        <el-form-item label="报告类型" prop="type">
          <el-select v-model="reportForm.type" placeholder="选择报告类型" style="width: 200px">
            <el-option label="安全概览报告" value="SECURITY_SUMMARY" />
            <el-option label="漏洞详情报告" value="VULNERABILITY_DETAIL" />
            <el-option label="合规性报告" value="COMPLIANCE" />
            <el-option label="趋势分析报告" value="TREND_ANALYSIS" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="项目范围" prop="projectIds">
          <el-select
            v-model="reportForm.projectIds"
            placeholder="选择项目"
            multiple
            style="width: 300px"
          >
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="时间范围" prop="timeRange">
          <el-date-picker
            v-model="timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>
        
        <el-form-item label="报告格式" prop="format">
          <el-select v-model="reportForm.format" placeholder="选择格式" style="width: 120px">
            <el-option label="PDF" value="PDF" />
            <el-option label="Excel" value="EXCEL" />
            <el-option label="HTML" value="HTML" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" :loading="generating" @click="handleGenerate">
            <el-icon><Document /></el-icon>
            生成报告
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 报告列表 -->
    <div class="table-container">
      <div class="table-header">
        <div class="table-search">
          <el-select
            v-model="searchForm.type"
            placeholder="报告类型"
            style="width: 150px"
            clearable
          >
            <el-option label="安全概览" value="SECURITY_SUMMARY" />
            <el-option label="漏洞详情" value="VULNERABILITY_DETAIL" />
            <el-option label="合规性" value="COMPLIANCE" />
            <el-option label="趋势分析" value="TREND_ANALYSIS" />
          </el-select>
          
          <el-select
            v-model="searchForm.status"
            placeholder="生成状态"
            style="width: 120px"
            clearable
          >
            <el-option label="生成中" value="GENERATING" />
            <el-option label="已完成" value="COMPLETED" />
            <el-option label="失败" value="FAILED" />
          </el-select>
          
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>
      </div>

      <!-- 报告表格 -->
      <el-table
        v-loading="loading"
        :data="reports"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column prop="name" label="报告名称" min-width="200" />
        
        <el-table-column label="报告类型" width="120">
          <template #default="{ row }">
            <span>{{ getReportTypeLabel(row.type) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="格式" width="80">
          <template #default="{ row }">
            <el-tag size="small">{{ row.format }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="项目范围" min-width="200">
          <template #default="{ row }">
            <span v-if="row.projectNames">{{ row.projectNames.join(', ') }}</span>
            <span v-else class="text-muted">全部项目</span>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getStatusTagType(row.status)"
              size="small"
            >
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column
          prop="createdAt"
          label="创建时间"
          width="180"
          :formatter="(row) => formatDate(row.createdAt)"
        />
        
        <el-table-column
          prop="completedAt"
          label="完成时间"
          width="180"
        >
          <template #default="{ row }">
            <span v-if="row.completedAt">{{ formatDate(row.completedAt) }}</span>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="row.status === 'COMPLETED'"
              type="primary"
              size="small"
              @click="handleDownload(row)"
            >
              <el-icon><Download /></el-icon>
              下载
            </el-button>
            
            <el-button
              v-if="row.status === 'COMPLETED'"
              type="success"
              size="small"
              @click="handlePreview(row)"
            >
              <el-icon><View /></el-icon>
              预览
            </el-button>
            
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
            >
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="table-pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElForm } from 'element-plus'
import { formatDate } from '@/utils'
import type { Report, Project } from '@/types'

// 状态
const loading = ref(false)
const generating = ref(false)
const reports = ref<Report[]>([])
const projects = ref<Project[]>([])
const timeRange = ref<[Date, Date] | null>(null)

// 表单引用
const reportFormRef = ref<InstanceType<typeof ElForm>>()

// 报告生成表单
const reportForm = reactive({
  type: '',
  projectIds: [] as number[],
  format: 'PDF',
  name: ''
})

// 表单验证规则
const reportRules = {
  type: [
    { required: true, message: '请选择报告类型', trigger: 'change' }
  ],
  format: [
    { required: true, message: '请选择报告格式', trigger: 'change' }
  ]
}

// 搜索表单
const searchForm = reactive({
  type: '',
  status: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 获取报告类型标签
const getReportTypeLabel = (type: string) => {
  const labels = {
    SECURITY_SUMMARY: '安全概览',
    VULNERABILITY_DETAIL: '漏洞详情',
    COMPLIANCE: '合规性',
    TREND_ANALYSIS: '趋势分析'
  }
  return labels[type as keyof typeof labels] || type
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const types = {
    GENERATING: 'primary',
    COMPLETED: 'success',
    FAILED: 'danger'
  }
  return types[status as keyof typeof types] || 'info'
}

// 获取状态标签
const getStatusLabel = (status: string) => {
  const labels = {
    GENERATING: '生成中',
    COMPLETED: '已完成',
    FAILED: '失败'
  }
  return labels[status as keyof typeof labels] || status
}

// 获取报告列表
const fetchReports = async () => {
  try {
    loading.value = true
    
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    
    // 这里调用API获取报告列表
    // const response = await reportsApi.getReports(params)
    // reports.value = response.data.list
    // pagination.total = response.data.total
    
    // 模拟数据
    reports.value = [
      {
        id: 1,
        name: '2024年1月安全概览报告',
        type: 'SECURITY_SUMMARY',
        format: 'PDF',
        projectIds: [1, 2],
        projectNames: ['Web应用项目', 'API服务项目'],
        timeRange: {
          startDate: '2024-01-01',
          endDate: '2024-01-31'
        },
        status: 'COMPLETED',
        filePath: '/reports/security-summary-202401.pdf',
        createdBy: 1,
        createdAt: '2024-01-15T10:30:00Z',
        completedAt: '2024-01-15T10:35:00Z'
      },
      {
        id: 2,
        name: '漏洞详情报告',
        type: 'VULNERABILITY_DETAIL',
        format: 'EXCEL',
        projectIds: [1],
        projectNames: ['Web应用项目'],
        timeRange: {
          startDate: '2024-01-01',
          endDate: '2024-01-15'
        },
        status: 'GENERATING',
        createdBy: 1,
        createdAt: '2024-01-15T11:00:00Z'
      }
    ]
    pagination.total = 2
  } catch (error) {
    ElMessage.error('获取报告列表失败')
  } finally {
    loading.value = false
  }
}

// 获取项目列表
const fetchProjects = async () => {
  try {
    // 这里调用API获取项目列表
    // const response = await projectsApi.getProjects()
    // projects.value = response.data.list
    
    // 模拟数据
    projects.value = [
      { id: 1, name: 'Web应用项目' },
      { id: 2, name: 'API服务项目' },
      { id: 3, name: '移动应用项目' }
    ] as Project[]
  } catch (error) {
    console.error('获取项目列表失败:', error)
  }
}

// 生成报告
const handleGenerate = async () => {
  if (!reportFormRef.value) return
  
  try {
    await reportFormRef.value.validate()
    
    generating.value = true
    
    const reportData = {
      ...reportForm,
      timeRange: timeRange.value ? {
        startDate: timeRange.value[0].toISOString().split('T')[0],
        endDate: timeRange.value[1].toISOString().split('T')[0]
      } : undefined
    }
    
    // 这里调用API生成报告
    // await reportsApi.generateReport(reportData)
    
    ElMessage.success('报告生成任务已提交，请稍后查看')
    
    // 重置表单
    Object.assign(reportForm, {
      type: '',
      projectIds: [],
      format: 'PDF',
      name: ''
    })
    timeRange.value = null
    reportFormRef.value.clearValidate()
    
    // 刷新列表
    fetchReports()
  } catch (error: any) {
    ElMessage.error(error.message || '生成报告失败')
  } finally {
    generating.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchReports()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    type: '',
    status: ''
  })
  pagination.page = 1
  fetchReports()
}

// 下载报告
const handleDownload = (report: Report) => {
  // 这里实现报告下载逻辑
  ElMessage.success('开始下载报告')
}

// 预览报告
const handlePreview = (report: Report) => {
  // 这里实现报告预览逻辑
  ElMessage.info('预览功能待实现')
}

// 删除报告
const handleDelete = async (report: Report) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除报告 "${report.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里调用API删除报告
    // await reportsApi.deleteReport(report.id)
    
    ElMessage.success('删除成功')
    fetchReports()
  } catch (error) {
    // 用户取消删除
  }
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  fetchReports()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchReports()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchReports()
  fetchProjects()
})
</script>

<style scoped>
.reports-page {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.report-generator {
  margin-bottom: 24px;
}

.table-container {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-search {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.text-muted {
  color: #999;
  font-size: 12px;
}

.table-pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .table-search {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>
