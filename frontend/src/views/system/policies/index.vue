<template>
  <div class="policies-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>安全策略</h2>
      <p>配置和管理安全扫描策略</p>
    </div>

    <div class="policies-content">
      <el-card shadow="hover">
        <template #header>
          <span>安全策略配置</span>
        </template>
        
        <div class="policy-placeholder">
          <el-empty description="安全策略配置功能正在开发中">
            <el-button type="primary">敬请期待</el-button>
          </el-empty>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
// 安全策略页面（待实现）
</script>

<style scoped>
.policies-page {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.policies-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.policy-placeholder {
  padding: 60px 0;
}
</style>
