<template>
  <div class="roles-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>角色管理</h2>
      <p>管理系统角色和权限配置</p>
    </div>

    <!-- 搜索和操作栏 -->
    <div class="table-container">
      <div class="table-header">
        <div class="table-search">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索角色名称或描述"
            style="width: 240px"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>
        
        <div class="table-actions">
          <el-button
            type="primary"
            @click="handleCreate"
            v-if="userStore.hasPermission('role:create')"
          >
            <el-icon><Plus /></el-icon>
            新增角色
          </el-button>
        </div>
      </div>

      <!-- 角色表格 -->
      <el-table
        v-loading="loading"
        :data="roles"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column prop="name" label="角色名称" min-width="150" />
        
        <el-table-column prop="description" label="角色描述" min-width="200">
          <template #default="{ row }">
            <span>{{ row.description || '暂无描述' }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="权限数量" width="100">
          <template #default="{ row }">
            <el-tag type="info" size="small">
              {{ row.permissions?.length || 0 }} 个权限
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="用户数量" width="100">
          <template #default="{ row }">
            <span>{{ row.userCount || 0 }} 人</span>
          </template>
        </el-table-column>
        
        <el-table-column
          prop="createdAt"
          label="创建时间"
          width="180"
          :formatter="(row) => formatDate(row.createdAt)"
        />
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(row)"
              v-if="userStore.hasPermission('role:update')"
            >
              编辑
            </el-button>
            
            <el-button
              type="info"
              size="small"
              @click="handleViewPermissions(row)"
            >
              权限
            </el-button>
            
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
              :disabled="row.userCount > 0"
              v-if="userStore.hasPermission('role:delete')"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="table-pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 角色表单对话框 -->
    <RoleFormDialog
      v-model:visible="formDialogVisible"
      :role="currentRole"
      @success="handleFormSuccess"
    />

    <!-- 权限查看对话框 -->
    <PermissionsDialog
      v-model:visible="permissionsDialogVisible"
      :role="currentRole"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { formatDate } from '@/utils'
import RoleFormDialog from './components/RoleFormDialog.vue'
import PermissionsDialog from './components/PermissionsDialog.vue'
import type { Role } from '@/types'

const userStore = useUserStore()

// 状态
const loading = ref(false)
const roles = ref<Role[]>([])
const formDialogVisible = ref(false)
const permissionsDialogVisible = ref(false)
const currentRole = ref<Role | null>(null)

// 搜索表单
const searchForm = reactive({
  keyword: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 获取角色列表
const fetchRoles = async () => {
  try {
    loading.value = true
    
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    
    // 这里调用API获取角色列表
    // const response = await rolesApi.getRoles(params)
    // roles.value = response.data.list
    // pagination.total = response.data.total
    
    // 模拟数据
    roles.value = [
      {
        id: 1,
        name: '系统管理员',
        description: '拥有系统所有权限的超级管理员',
        permissions: [
          { id: 1, code: '*', name: '所有权限', resource: '*', action: '*', description: '系统所有权限' }
        ],
        userCount: 2,
        createdAt: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        name: '安全工程师',
        description: '负责安全扫描和漏洞管理的工程师',
        permissions: [
          { id: 2, code: 'project:read', name: '项目查看', resource: 'project', action: 'read', description: '查看项目信息' },
          { id: 3, code: 'scan:manage', name: '扫描管理', resource: 'scan', action: 'manage', description: '管理扫描任务' },
          { id: 4, code: 'vulnerability:manage', name: '漏洞管理', resource: 'vulnerability', action: 'manage', description: '管理漏洞' }
        ],
        userCount: 5,
        createdAt: '2024-01-02T00:00:00Z'
      },
      {
        id: 3,
        name: '开发者',
        description: '项目开发人员，可查看项目相关信息',
        permissions: [
          { id: 5, code: 'project:read', name: '项目查看', resource: 'project', action: 'read', description: '查看项目信息' },
          { id: 6, code: 'vulnerability:read', name: '漏洞查看', resource: 'vulnerability', action: 'read', description: '查看漏洞信息' }
        ],
        userCount: 15,
        createdAt: '2024-01-03T00:00:00Z'
      }
    ]
    pagination.total = 3
  } catch (error) {
    ElMessage.error('获取角色列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchRoles()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: ''
  })
  pagination.page = 1
  fetchRoles()
}

// 新增角色
const handleCreate = () => {
  currentRole.value = null
  formDialogVisible.value = true
}

// 编辑角色
const handleEdit = (role: Role) => {
  currentRole.value = role
  formDialogVisible.value = true
}

// 查看权限
const handleViewPermissions = (role: Role) => {
  currentRole.value = role
  permissionsDialogVisible.value = true
}

// 删除角色
const handleDelete = async (role: Role) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除角色 "${role.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里调用API删除角色
    // await rolesApi.deleteRole(role.id)
    
    ElMessage.success('删除成功')
    fetchRoles()
  } catch (error) {
    // 用户取消删除
  }
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  fetchRoles()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchRoles()
}

// 表单提交成功
const handleFormSuccess = () => {
  formDialogVisible.value = false
  fetchRoles()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchRoles()
})
</script>

<style scoped>
.roles-page {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.table-container {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-search {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.table-actions {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

.table-pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .table-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .table-search {
    flex-direction: column;
    align-items: stretch;
  }
  
  .table-actions {
    justify-content: flex-start;
  }
}
</style>
