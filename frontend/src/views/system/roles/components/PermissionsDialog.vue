<template>
  <el-dialog
    :model-value="visible"
    :title="`${role?.name} - 权限详情`"
    width="800px"
    @update:model-value="$emit('update:visible', $event)"
  >
    <div class="permissions-content">
      <div v-if="role?.permissions?.length">
        <el-table :data="role.permissions" style="width: 100%">
          <el-table-column prop="name" label="权限名称" />
          <el-table-column prop="code" label="权限代码" />
          <el-table-column prop="resource" label="资源" />
          <el-table-column prop="action" label="操作" />
          <el-table-column prop="description" label="描述" />
        </el-table>
      </div>
      <div v-else class="no-permissions">
        <p>该角色暂无权限</p>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import type { Role } from '@/types'

interface Props {
  visible: boolean
  role?: Role | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>

<style scoped>
.permissions-content {
  min-height: 200px;
}

.no-permissions {
  text-align: center;
  color: #666;
  padding: 40px 0;
}

.dialog-footer {
  text-align: right;
}
</style>
