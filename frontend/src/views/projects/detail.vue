<template>
  <div class="project-detail-page">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>
    
    <div v-else-if="project" class="project-content">
      <!-- 项目基本信息 -->
      <el-card class="project-info-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <div class="project-title">
              <h2>{{ project.name }}</h2>
              <el-tag
                :type="project.status === 'ACTIVE' ? 'success' : 'warning'"
                size="large"
              >
                {{ project.status === 'ACTIVE' ? '活跃' : '禁用' }}
              </el-tag>
            </div>
            <div class="project-actions">
              <el-button
                type="primary"
                @click="handleScan"
                v-if="userStore.hasPermission('scan:create')"
              >
                <el-icon><Search /></el-icon>
                立即扫描
              </el-button>
              <el-button
                @click="handleEdit"
                v-if="userStore.hasPermission('project:update')"
              >
                <el-icon><Edit /></el-icon>
                编辑项目
              </el-button>
            </div>
          </div>
        </template>
        
        <div class="project-details">
          <el-row :gutter="24">
            <el-col :span="12">
              <div class="detail-item">
                <label>项目描述：</label>
                <span>{{ project.description || '暂无描述' }}</span>
              </div>
              <div class="detail-item">
                <label>代码仓库：</label>
                <el-link :href="project.repositoryUrl" target="_blank" type="primary">
                  {{ project.repositoryUrl }}
                </el-link>
              </div>
              <div class="detail-item">
                <label>默认分支：</label>
                <span>{{ project.branch }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>开发语言：</label>
                <div class="language-tags">
                  <el-tag
                    v-for="lang in project.language"
                    :key="lang"
                    size="small"
                    style="margin-right: 8px"
                  >
                    {{ lang }}
                  </el-tag>
                </div>
              </div>
              <div class="detail-item">
                <label>创建时间：</label>
                <span>{{ formatDate(project.createdAt) }}</span>
              </div>
              <div class="detail-item">
                <label>更新时间：</label>
                <span>{{ formatDate(project.updatedAt) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 安全概览 -->
      <el-row :gutter="20" class="security-overview">
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ projectStats.securityScore }}</div>
              <div class="metric-label">安全评分</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ projectStats.totalVulnerabilities }}</div>
              <div class="metric-label">总漏洞数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ projectStats.totalScans }}</div>
              <div class="metric-label">扫描次数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ projectStats.lastScanTime ? formatRelativeTime(projectStats.lastScanTime) : '未扫描' }}</div>
              <div class="metric-label">最后扫描</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 标签页内容 -->
      <el-tabs v-model="activeTab" class="project-tabs">
        <el-tab-pane label="扫描历史" name="scans">
          <div class="tab-content">
            <p>扫描历史内容（待实现）</p>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="漏洞列表" name="vulnerabilities">
          <div class="tab-content">
            <p>漏洞列表内容（待实现）</p>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="扫描配置" name="config">
          <div class="tab-content">
            <p>扫描配置内容（待实现）</p>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <div v-else class="error-container">
      <el-result
        icon="warning"
        title="项目不存在"
        sub-title="请检查项目ID是否正确"
      >
        <template #extra>
          <el-button type="primary" @click="$router.push('/projects')">
            返回项目列表
          </el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { formatDate, formatRelativeTime } from '@/utils'
import type { Project } from '@/types'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 状态
const loading = ref(true)
const project = ref<Project | null>(null)
const activeTab = ref('scans')

// 项目统计数据
const projectStats = ref({
  securityScore: 0,
  totalVulnerabilities: 0,
  totalScans: 0,
  criticalVulns: 0,
  highVulns: 0,
  mediumVulns: 0,
  lowVulns: 0,
  lastScanTime: null as string | null
})

// 获取项目详情
const fetchProjectDetail = async () => {
  try {
    loading.value = true
    const projectId = Number(route.params.id)
    
    // 这里调用API获取项目详情
    // const response = await projectsApi.getProject(projectId)
    // project.value = response.data
    
    // 模拟数据
    project.value = {
      id: projectId,
      name: 'Web应用项目',
      description: '公司官网前端项目，使用Vue 3开发',
      repositoryUrl: 'https://github.com/company/web-app.git',
      branch: 'main',
      language: ['JavaScript', 'Vue', 'TypeScript'],
      scanConfig: {
        sastEnabled: true,
        dastEnabled: true,
        scaEnabled: true,
        containerEnabled: false,
        iacEnabled: false,
        qualityGate: {
          criticalThreshold: 0,
          highThreshold: 5,
          mediumThreshold: 20,
          lowThreshold: 50,
          blockBuild: true
        }
      },
      status: 'ACTIVE',
      createdBy: 1,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-15T10:30:00Z'
    }
    
    // 获取项目统计数据
    await fetchProjectStats(projectId)
    
  } catch (error) {
    ElMessage.error('获取项目详情失败')
    project.value = null
  } finally {
    loading.value = false
  }
}

// 获取项目统计数据
const fetchProjectStats = async (projectId: number) => {
  try {
    // 这里调用API获取项目统计数据
    // const response = await projectsApi.getProjectStats(projectId)
    // projectStats.value = response.data
    
    // 模拟数据
    projectStats.value = {
      securityScore: 85,
      totalVulnerabilities: 25,
      totalScans: 15,
      criticalVulns: 2,
      highVulns: 5,
      mediumVulns: 8,
      lowVulns: 10,
      lastScanTime: '2024-01-15T10:30:00Z'
    }
  } catch (error) {
    console.error('获取项目统计数据失败:', error)
  }
}

// 扫描项目
const handleScan = () => {
  router.push(`/scans?projectId=${project.value?.id}&action=create`)
}

// 编辑项目
const handleEdit = () => {
  router.push(`/projects/${project.value?.id}/edit`)
}

// 组件挂载时获取数据
onMounted(() => {
  fetchProjectDetail()
})
</script>

<style scoped>
.project-detail-page {
  padding: 0;
}

.loading-container {
  padding: 24px;
}

.project-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.project-info-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.project-title {
  display: flex;
  align-items: center;
  gap: 16px;
}

.project-title h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.project-actions {
  display: flex;
  gap: 12px;
}

.project-details {
  padding: 16px 0;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  gap: 8px;
}

.detail-item label {
  min-width: 80px;
  font-weight: 500;
  color: #666;
}

.detail-item span {
  color: #333;
}

.language-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.security-overview {
  margin-bottom: 24px;
}

.metric-card {
  text-align: center;
}

.metric-content {
  padding: 16px;
}

.metric-value {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.metric-label {
  font-size: 14px;
  color: #666;
}

.project-tabs {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-content {
  padding: 24px 0;
  min-height: 300px;
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .project-actions {
    justify-content: center;
  }
  
  .detail-item {
    flex-direction: column;
    gap: 4px;
  }
  
  .detail-item label {
    min-width: auto;
  }
}
</style>
