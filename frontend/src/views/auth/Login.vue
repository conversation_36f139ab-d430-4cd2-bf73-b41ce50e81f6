<template>
  <div class="login-container">
    <div class="login-background">
      <div class="background-overlay"></div>
    </div>
    
    <div class="login-content">
      <!-- Logo和标题 -->
      <div class="login-header">
        <img src="/logo.svg" alt="SDL" class="logo" />
        <h1 class="title">SDL安全开发生命周期平台</h1>
        <p class="subtitle">企业级安全管理解决方案</p>
      </div>
      
      <!-- 登录表单 -->
      <div class="login-form-container">
        <el-card class="login-card" shadow="always">
          <template #header>
            <div class="card-header">
              <h2>用户登录</h2>
              <div class="demo-info">
                <el-alert
                  title="演示账号"
                  description="用户名: admin, 密码: admin123"
                  type="info"
                  :closable="false"
                  show-icon
                />
              </div>
            </div>
          </template>
          
          <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="loginRules"
            size="large"
            @keyup.enter="handleLogin"
          >
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                placeholder="请输入用户名"
                prefix-icon="User"
                clearable
              />
            </el-form-item>
            
            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                prefix-icon="Lock"
                show-password
                clearable
              />
            </el-form-item>
            
            <!-- 验证码 -->
            <el-form-item v-if="showCaptcha" prop="captcha">
              <div class="captcha-container">
                <el-input
                  v-model="loginForm.captcha"
                  placeholder="请输入验证码"
                  prefix-icon="Picture"
                  clearable
                  style="flex: 1"
                />
                <div class="captcha-image" @click="refreshCaptcha">
                  <img v-if="captchaImage" :src="captchaImage" alt="验证码" />
                  <span v-else>点击获取</span>
                </div>
              </div>
            </el-form-item>
            
            <!-- 记住我 -->
            <el-form-item>
              <div class="login-options">
                <el-checkbox v-model="rememberMe">记住我</el-checkbox>
                <el-link type="primary" :underline="false">忘记密码？</el-link>
              </div>
            </el-form-item>
            
            <!-- 登录按钮 -->
            <el-form-item>
              <el-button
                type="primary"
                size="large"
                :loading="loading"
                @click="handleLogin"
                class="login-button"
              >
                {{ loading ? '登录中...' : '登录' }}
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElForm } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { authApi } from '@/api/auth'
import type { LoginForm } from '@/types'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 表单引用
const loginFormRef = ref<InstanceType<typeof ElForm>>()

// 表单数据
const loginForm = reactive<LoginForm>({
  username: '',
  password: '',
  captcha: ''
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 4, message: '验证码长度为 4 位', trigger: 'blur' }
  ]
}

// 状态
const loading = ref(false)
const rememberMe = ref(false)
const showCaptcha = ref(false)
const captchaImage = ref('')
const captchaId = ref('')

// 获取验证码
const getCaptcha = async () => {
  try {
    // 开发环境跳过验证码
    if (import.meta.env.VITE_APP_ENV === 'development') {
      showCaptcha.value = false
      return
    }

    const response = await authApi.getCaptcha()
    captchaImage.value = response.data.captchaImage
    captchaId.value = response.data.captchaId
    showCaptcha.value = true
  } catch (error) {
    console.error('获取验证码失败:', error)
    // 开发环境出错时也跳过验证码
    if (import.meta.env.VITE_APP_ENV === 'development') {
      showCaptcha.value = false
    }
  }
}

// 刷新验证码
const refreshCaptcha = () => {
  getCaptcha()
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    // 开发环境下跳过验证码验证
    if (import.meta.env.VITE_APP_ENV === 'development') {
      // 只验证用户名和密码
      const fieldsToValidate = ['username', 'password']
      for (const field of fieldsToValidate) {
        await loginFormRef.value.validateField(field)
      }
    } else {
      await loginFormRef.value.validate()
    }

    loading.value = true

    const loginData = {
      ...loginForm,
      captchaId: captchaId.value
    }

    await userStore.login(loginData)

    ElMessage.success('登录成功')

    // 跳转到目标页面或首页
    const redirect = route.query.redirect as string
    router.push(redirect || '/')

  } catch (error: any) {
    console.error('登录失败:', error)

    // 如果是验证码错误，刷新验证码
    if (error.message?.includes('验证码')) {
      refreshCaptcha()
      loginForm.captcha = ''
    }

    ElMessage.error(error.message || '登录失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取验证码
onMounted(() => {
  // 开发环境可以预填充测试数据
  if (import.meta.env.VITE_APP_ENV === 'development') {
    loginForm.username = 'admin'
    loginForm.password = 'admin123'
  }
  
  // 获取验证码
  getCaptcha()
})
</script>

<style scoped>
.login-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-size: cover;
  background-position: center;
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
}

.login-content {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.logo {
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
}

.title {
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 10px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

.login-form-container {
  width: 100%;
  max-width: 400px;
}

.login-card {
  border-radius: 12px;
  overflow: hidden;
}

.card-header {
  text-align: center;
}

.card-header h2 {
  margin: 0 0 16px 0;
  color: #333;
  font-weight: 600;
}

.demo-info {
  margin-bottom: 0;
}

.captcha-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.captcha-image {
  width: 100px;
  height: 40px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  transition: border-color 0.3s;
}

.captcha-image:hover {
  border-color: #409eff;
}

.captcha-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.captcha-image span {
  font-size: 12px;
  color: #999;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-content {
    padding: 10px;
  }
  
  .title {
    font-size: 24px;
  }
  
  .subtitle {
    font-size: 14px;
  }
  
  .login-form-container {
    max-width: 100%;
  }
}
</style>
