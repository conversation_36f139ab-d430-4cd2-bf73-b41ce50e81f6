{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "auto-imports.d.ts", "components.d.ts"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@components/*": ["./src/components/*"], "@views/*": ["./src/views/*"], "@stores/*": ["./src/stores/*"], "@utils/*": ["./src/utils/*"], "@api/*": ["./src/api/*"], "@types/*": ["./src/types/*"]}, "types": ["node", "element-plus/global"], "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true}}