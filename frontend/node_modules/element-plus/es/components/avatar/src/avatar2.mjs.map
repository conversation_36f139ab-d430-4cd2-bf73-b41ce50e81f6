{"version": 3, "file": "avatar2.mjs", "sources": ["../../../../../../packages/components/avatar/src/avatar.ts"], "sourcesContent": ["import {\n  buildProps,\n  definePropType,\n  iconPropType,\n  isNumber,\n} from '@element-plus/utils'\nimport { componentSizes } from '@element-plus/constants'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type { ObjectFitProperty } from 'csstype'\n\nexport const avatarProps = buildProps({\n  /**\n   * @description avatar size.\n   */\n  size: {\n    type: [Number, String],\n    values: componentSizes,\n    default: '',\n    validator: (val: unknown): val is number => isNumber(val),\n  },\n  /**\n   * @description avatar shape.\n   */\n  shape: {\n    type: String,\n    values: ['circle', 'square'],\n    default: 'circle',\n  },\n  /**\n   * @description representation type to icon, more info on icon component.\n   */\n  icon: {\n    type: iconPropType,\n  },\n  /**\n   * @description the source of the image for an image avatar.\n   */\n  src: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description native attribute `alt` of image avatar.\n   */\n  alt: String,\n  /**\n   * @description native attribute srcset of image avatar.\n   */\n  srcSet: String,\n  /**\n   * @description set how the image fit its container for an image avatar.\n   */\n  fit: {\n    type: definePropType<ObjectFitProperty>(String),\n    default: 'cover',\n  },\n} as const)\nexport type AvatarProps = ExtractPropTypes<typeof avatarProps>\nexport type AvatarPropsPublic = __ExtractPublicPropTypes<typeof avatarProps>\n\nexport const avatarEmits = {\n  error: (evt: Event) => evt instanceof Event,\n}\nexport type AvatarEmits = typeof avatarEmits\n"], "names": [], "mappings": ";;;;;AAOY,MAAC,WAAW,GAAG,UAAU,CAAC;AACtC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,MAAM,EAAE,cAAc;AAC1B,IAAI,OAAO,EAAE,EAAE;AACf,IAAI,SAAS,EAAE,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC;AACrC,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;AAChC,IAAI,OAAO,EAAE,QAAQ;AACrB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,YAAY;AACtB,GAAG;AACH,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,GAAG,EAAE,MAAM;AACb,EAAE,MAAM,EAAE,MAAM;AAChB,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,WAAW,GAAG;AAC3B,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,KAAK;AACtC;;;;"}