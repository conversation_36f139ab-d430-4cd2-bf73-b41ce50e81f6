{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/avatar/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Avatar from './src/avatar.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElAvatar: SFCWithInstall<typeof Avatar> = withInstall(Avatar)\nexport default ElAvatar\n\nexport * from './src/avatar'\nexport type { AvatarInstance } from './src/instance'\n"], "names": [], "mappings": ";;;;AAEY,MAAC,QAAQ,GAAG,WAAW,CAAC,MAAM;;;;"}