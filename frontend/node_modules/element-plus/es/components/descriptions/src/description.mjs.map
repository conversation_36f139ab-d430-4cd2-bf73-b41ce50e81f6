{"version": 3, "file": "description.mjs", "sources": ["../../../../../../packages/components/descriptions/src/description.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { useSizeProp } from '@element-plus/hooks'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type Description from './description.vue'\n\nexport const descriptionProps = buildProps({\n  /**\n   * @description with or without border\n   */\n  border: Boolean,\n  /**\n   * @description numbers of `Descriptions Item` in one line\n   */\n  column: {\n    type: Number,\n    default: 3,\n  },\n  /**\n   * @description direction of list\n   */\n  direction: {\n    type: String,\n    values: ['horizontal', 'vertical'],\n    default: 'horizontal',\n  },\n  /**\n   * @description size of list\n   */\n  size: useSizeProp,\n  /**\n   * @description title text, display on the top left\n   */\n  title: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description extra text, display on the top right\n   */\n  extra: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description width of every label column\n   */\n  labelWidth: {\n    type: [String, Number],\n    default: '',\n  },\n} as const)\n\nexport type DescriptionProps = ExtractPropTypes<typeof descriptionProps>\nexport type DescriptionPropsPublic = __ExtractPublicPropTypes<\n  typeof descriptionProps\n>\nexport type DescriptionInstance = InstanceType<typeof Description> & unknown\n"], "names": [], "mappings": ";;;AAEY,MAAC,gBAAgB,GAAG,UAAU,CAAC;AAC3C,EAAE,MAAM,EAAE,OAAO;AACjB,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;AACtC,IAAI,OAAO,EAAE,YAAY;AACzB,GAAG;AACH,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,CAAC;;;;"}