import Dialog from './src/dialog2.mjs';
export { useDialog } from './src/use-dialog.mjs';
export { dialogContextKey, dialogEmits, dialogProps } from './src/dialog.mjs';
export { DEFAULT_DIALOG_TRANSITION, dialogInjectionKey } from './src/constants.mjs';
import { withInstall } from '../../utils/vue/install.mjs';

const ElDialog = withInstall(Dialog);

export { ElDialog, ElDialog as default };
//# sourceMappingURL=index.mjs.map
