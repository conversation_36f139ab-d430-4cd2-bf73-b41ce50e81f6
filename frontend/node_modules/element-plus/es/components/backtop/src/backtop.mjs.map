{"version": 3, "file": "backtop.mjs", "sources": ["../../../../../../packages/components/backtop/src/backtop.vue"], "sourcesContent": ["<template>\n  <transition :name=\"`${ns.namespace.value}-fade-in`\">\n    <div\n      v-if=\"visible\"\n      :style=\"backTopStyle\"\n      :class=\"ns.b()\"\n      @click.stop=\"handleClick\"\n    >\n      <slot>\n        <el-icon :class=\"ns.e('icon')\"><caret-top /></el-icon>\n      </slot>\n    </div>\n  </transition>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { CaretTop } from '@element-plus/icons-vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { backtopEmits, backtopProps } from './backtop'\nimport { useBackTop } from './use-backtop'\n\nconst COMPONENT_NAME = 'ElBacktop'\n\ndefineOptions({\n  name: COMPONENT_NAME,\n})\n\nconst props = defineProps(backtopProps)\nconst emit = defineEmits(backtopEmits)\n\nconst ns = useNamespace('backtop')\n\nconst { handleClick, visible } = useBackTop(props, emit, COMPONENT_NAME)\n\nconst backTopStyle = computed(() => ({\n  right: `${props.right}px`,\n  bottom: `${props.bottom}px`,\n}))\n</script>\n"], "names": [], "mappings": ";;;;;;;;;mCAyBc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;;AAKA,IAAM,MAAA,EAAA,GAAK,aAAa,SAAS,CAAA,CAAA;AAEjC,IAAA,MAAM,EAAE,WAAa,EAAA,OAAA,KAAY,UAAW,CAAA,KAAA,EAAO,MAAM,cAAc,CAAA,CAAA;AAEvE,IAAM,MAAA,YAAA,GAAe,SAAS,OAAO;AAAA,MACnC,KAAA,EAAO,CAAG,EAAA,KAAA,CAAM,KAAK,CAAA,EAAA,CAAA;AAAA,MACrB,MAAA,EAAQ,CAAG,EAAA,KAAA,CAAM,MAAM,CAAA,EAAA,CAAA;AAAA,KACvB,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}