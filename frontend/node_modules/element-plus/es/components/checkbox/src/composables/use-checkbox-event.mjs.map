{"version": 3, "file": "use-checkbox-event.mjs", "sources": ["../../../../../../../packages/components/checkbox/src/composables/use-checkbox-event.ts"], "sourcesContent": ["import { computed, getCurrentInstance, inject, nextTick, watch } from 'vue'\nimport { useFormItem } from '@element-plus/components/form'\nimport { debugWarn } from '@element-plus/utils'\nimport { CHANGE_EVENT } from '@element-plus/constants'\nimport { checkboxGroupContextKey } from '../constants'\n\nimport type { useFormItemInputId } from '@element-plus/components/form'\nimport type { CheckboxProps } from '../checkbox'\nimport type {\n  CheckboxDisabled,\n  CheckboxModel,\n  CheckboxStatus,\n} from '../composables'\n\nexport const useCheckboxEvent = (\n  props: CheckboxProps,\n  {\n    model,\n    isLimitExceeded,\n    hasOwnLabel,\n    isDisabled,\n    isLabeledByFormItem,\n  }: Pick<CheckboxModel, 'model' | 'isLimitExceeded'> &\n    Pick<CheckboxStatus, 'hasOwnLabel'> &\n    Pick<CheckboxDisabled, 'isDisabled'> &\n    Pick<ReturnType<typeof useFormItemInputId>, 'isLabeledByFormItem'>\n) => {\n  const checkboxGroup = inject(checkboxGroupContextKey, undefined)\n  const { formItem } = useFormItem()\n  const { emit } = getCurrentInstance()!\n\n  function getLabeledValue(value: string | number | boolean) {\n    return [true, props.trueValue, props.trueLabel].includes(value)\n      ? props.trueValue ?? props.trueLabel ?? true\n      : props.falseValue ?? props.falseLabel ?? false\n  }\n\n  function emitChangeEvent(\n    checked: string | number | boolean,\n    e: InputEvent | MouseEvent\n  ) {\n    emit(CHANGE_EVENT, getLabeledValue(checked), e)\n  }\n\n  function handleChange(e: Event) {\n    if (isLimitExceeded.value) return\n\n    const target = e.target as HTMLInputElement\n    emit(CHANGE_EVENT, getLabeledValue(target.checked), e)\n  }\n\n  async function onClickRoot(e: MouseEvent) {\n    if (isLimitExceeded.value) return\n\n    if (!hasOwnLabel.value && !isDisabled.value && isLabeledByFormItem.value) {\n      // fix: https://github.com/element-plus/element-plus/issues/9981\n      const eventTargets: EventTarget[] = e.composedPath()\n      const hasLabel = eventTargets.some(\n        (item) => (item as HTMLElement).tagName === 'LABEL'\n      )\n      if (!hasLabel) {\n        model.value = getLabeledValue(\n          [false, props.falseValue, props.falseLabel].includes(model.value)\n        )\n        await nextTick()\n        emitChangeEvent(model.value, e)\n      }\n    }\n  }\n\n  const validateEvent = computed(\n    () => checkboxGroup?.validateEvent || props.validateEvent\n  )\n\n  watch(\n    () => props.modelValue,\n    () => {\n      if (validateEvent.value) {\n        formItem?.validate('change').catch((err) => debugWarn(err))\n      }\n    }\n  )\n\n  return {\n    handleChange,\n    onClickRoot,\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAKY,MAAC,gBAAgB,GAAG,CAAC,KAAK,EAAE;AACxC,EAAE,KAAK;AACP,EAAE,eAAe;AACjB,EAAE,WAAW;AACb,EAAE,UAAU;AACZ,EAAE,mBAAmB;AACrB,CAAC,KAAK;AACN,EAAE,MAAM,aAAa,GAAG,MAAM,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC,CAAC;AAChE,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAW,EAAE,CAAC;AACrC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,kBAAkB,EAAE,CAAC;AACxC,EAAE,SAAS,eAAe,CAAC,KAAK,EAAE;AAClC,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACvB,IAAI,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,SAAS,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC,SAAS,KAAK,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC;AAC7O,GAAG;AACH,EAAE,SAAS,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE;AACvC,IAAI,IAAI,CAAC,YAAY,EAAE,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AACpD,GAAG;AACH,EAAE,SAAS,YAAY,CAAC,CAAC,EAAE;AAC3B,IAAI,IAAI,eAAe,CAAC,KAAK;AAC7B,MAAM,OAAO;AACb,IAAI,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;AAC5B,IAAI,IAAI,CAAC,YAAY,EAAE,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3D,GAAG;AACH,EAAE,eAAe,WAAW,CAAC,CAAC,EAAE;AAChC,IAAI,IAAI,eAAe,CAAC,KAAK;AAC7B,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI,mBAAmB,CAAC,KAAK,EAAE;AAC9E,MAAM,MAAM,YAAY,GAAG,CAAC,CAAC,YAAY,EAAE,CAAC;AAC5C,MAAM,MAAM,QAAQ,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,KAAK,OAAO,CAAC,CAAC;AAC7E,MAAM,IAAI,CAAC,QAAQ,EAAE;AACrB,QAAQ,KAAK,CAAC,KAAK,GAAG,eAAe,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACzG,QAAQ,MAAM,QAAQ,EAAE,CAAC;AACzB,QAAQ,eAAe,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACxC,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,aAAa,KAAK,KAAK,CAAC,aAAa,CAAC,CAAC;AAC9H,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,UAAU,EAAE,MAAM;AACtC,IAAI,IAAI,aAAa,CAAC,KAAK,EAAE;AAC7B,MAAM,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,SAAS,CAAI,CAAC,CAAC,CAAC;AAC7F,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,GAAG,CAAC;AACJ;;;;"}