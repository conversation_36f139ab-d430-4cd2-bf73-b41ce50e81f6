{"version": 3, "file": "panel-date-pick.mjs", "sources": ["../../../../../../../packages/components/date-picker/src/date-picker-com/panel-date-pick.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[\n      ppNs.b(),\n      dpNs.b(),\n      {\n        'has-sidebar': $slots.sidebar || hasShortcuts,\n        'has-time': showTime,\n      },\n    ]\"\n  >\n    <div :class=\"ppNs.e('body-wrapper')\">\n      <slot name=\"sidebar\" :class=\"ppNs.e('sidebar')\" />\n      <div v-if=\"hasShortcuts\" :class=\"ppNs.e('sidebar')\">\n        <button\n          v-for=\"(shortcut, key) in shortcuts\"\n          :key=\"key\"\n          type=\"button\"\n          :class=\"ppNs.e('shortcut')\"\n          @click=\"handleShortcutClick(shortcut)\"\n        >\n          {{ shortcut.text }}\n        </button>\n      </div>\n      <div :class=\"ppNs.e('body')\">\n        <div v-if=\"showTime\" :class=\"dpNs.e('time-header')\">\n          <span :class=\"dpNs.e('editor-wrap')\">\n            <el-input\n              :placeholder=\"t('el.datepicker.selectDate')\"\n              :model-value=\"visibleDate\"\n              size=\"small\"\n              :validate-event=\"false\"\n              @input=\"(val) => (userInputDate = val)\"\n              @change=\"handleVisibleDateChange\"\n            />\n          </span>\n          <span\n            v-click-outside=\"handleTimePickClose\"\n            :class=\"dpNs.e('editor-wrap')\"\n          >\n            <el-input\n              :placeholder=\"t('el.datepicker.selectTime')\"\n              :model-value=\"visibleTime\"\n              size=\"small\"\n              :validate-event=\"false\"\n              @focus=\"onTimePickerInputFocus\"\n              @input=\"(val) => (userInputTime = val)\"\n              @change=\"handleVisibleTimeChange\"\n            />\n            <time-pick-panel\n              :visible=\"timePickerVisible\"\n              :format=\"timeFormat\"\n              :parsed-value=\"innerDate\"\n              @pick=\"handleTimePick\"\n            />\n          </span>\n        </div>\n        <div\n          v-show=\"currentView !== 'time'\"\n          :class=\"[\n            dpNs.e('header'),\n            (currentView === 'year' || currentView === 'month') &&\n              dpNs.e('header--bordered'),\n          ]\"\n        >\n          <span :class=\"dpNs.e('prev-btn')\">\n            <button\n              type=\"button\"\n              :aria-label=\"t(`el.datepicker.prevYear`)\"\n              class=\"d-arrow-left\"\n              :class=\"ppNs.e('icon-btn')\"\n              @click=\"moveByYear(false)\"\n            >\n              <slot name=\"prev-year\">\n                <el-icon><d-arrow-left /></el-icon>\n              </slot>\n            </button>\n            <button\n              v-show=\"currentView === 'date'\"\n              type=\"button\"\n              :aria-label=\"t(`el.datepicker.prevMonth`)\"\n              :class=\"ppNs.e('icon-btn')\"\n              class=\"arrow-left\"\n              @click=\"moveByMonth(false)\"\n            >\n              <slot name=\"prev-month\">\n                <el-icon><arrow-left /></el-icon>\n              </slot>\n            </button>\n          </span>\n          <span\n            role=\"button\"\n            :class=\"dpNs.e('header-label')\"\n            aria-live=\"polite\"\n            tabindex=\"0\"\n            @keydown.enter=\"showPicker('year')\"\n            @click=\"showPicker('year')\"\n            >{{ yearLabel }}</span\n          >\n          <span\n            v-show=\"currentView === 'date'\"\n            role=\"button\"\n            aria-live=\"polite\"\n            tabindex=\"0\"\n            :class=\"[\n              dpNs.e('header-label'),\n              { active: currentView === 'month' },\n            ]\"\n            @keydown.enter=\"showPicker('month')\"\n            @click=\"showPicker('month')\"\n            >{{ t(`el.datepicker.month${month + 1}`) }}</span\n          >\n          <span :class=\"dpNs.e('next-btn')\">\n            <button\n              v-show=\"currentView === 'date'\"\n              type=\"button\"\n              :aria-label=\"t(`el.datepicker.nextMonth`)\"\n              :class=\"ppNs.e('icon-btn')\"\n              class=\"arrow-right\"\n              @click=\"moveByMonth(true)\"\n            >\n              <slot name=\"next-month\">\n                <el-icon><arrow-right /></el-icon>\n              </slot>\n            </button>\n            <button\n              type=\"button\"\n              :aria-label=\"t(`el.datepicker.nextYear`)\"\n              :class=\"ppNs.e('icon-btn')\"\n              class=\"d-arrow-right\"\n              @click=\"moveByYear(true)\"\n            >\n              <slot name=\"next-year\">\n                <el-icon><d-arrow-right /></el-icon>\n              </slot>\n            </button>\n          </span>\n        </div>\n        <div :class=\"ppNs.e('content')\" @keydown=\"handleKeydownTable\">\n          <date-table\n            v-if=\"currentView === 'date'\"\n            ref=\"currentViewRef\"\n            :selection-mode=\"selectionMode\"\n            :date=\"innerDate\"\n            :parsed-value=\"parsedValue\"\n            :disabled-date=\"disabledDate\"\n            :cell-class-name=\"cellClassName\"\n            :show-week-number=\"showWeekNumber\"\n            @pick=\"handleDatePick\"\n          />\n          <year-table\n            v-if=\"currentView === 'year'\"\n            ref=\"currentViewRef\"\n            :selection-mode=\"selectionMode\"\n            :date=\"innerDate\"\n            :disabled-date=\"disabledDate\"\n            :parsed-value=\"parsedValue\"\n            @pick=\"handleYearPick\"\n          />\n          <month-table\n            v-if=\"currentView === 'month'\"\n            ref=\"currentViewRef\"\n            :selection-mode=\"selectionMode\"\n            :date=\"innerDate\"\n            :parsed-value=\"parsedValue\"\n            :disabled-date=\"disabledDate\"\n            @pick=\"handleMonthPick\"\n          />\n        </div>\n      </div>\n    </div>\n    <div v-if=\"showFooter && footerVisible\" :class=\"ppNs.e('footer')\">\n      <el-button\n        v-show=\"!isMultipleType && showNow\"\n        text\n        size=\"small\"\n        :class=\"ppNs.e('link-btn')\"\n        :disabled=\"disabledNow\"\n        @click=\"changeToNow\"\n      >\n        {{ t('el.datepicker.now') }}\n      </el-button>\n      <el-button\n        plain\n        size=\"small\"\n        :class=\"ppNs.e('link-btn')\"\n        :disabled=\"disabledConfirm\"\n        @click=\"onConfirm\"\n      >\n        {{ t('el.datepicker.confirm') }}\n      </el-button>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  inject,\n  nextTick,\n  ref,\n  toRef,\n  useAttrs,\n  useSlots,\n  watch,\n} from 'vue'\nimport dayjs from 'dayjs'\nimport ElButton from '@element-plus/components/button'\nimport { ClickOutside as vClickOutside } from '@element-plus/directives'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport ElInput from '@element-plus/components/input'\nimport {\n  PICKER_BASE_INJECTION_KEY,\n  TimePickPanel,\n  extractDateFormat,\n  extractTimeFormat,\n} from '@element-plus/components/time-picker'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { isArray, isFunction } from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport {\n  ArrowLeft,\n  ArrowRight,\n  DArrowLeft,\n  DArrowRight,\n} from '@element-plus/icons-vue'\nimport { TOOLTIP_INJECTION_KEY } from '@element-plus/components/tooltip'\nimport { panelDatePickProps } from '../props/panel-date-pick'\nimport {\n  correctlyParseUserInput,\n  getValidDateOfMonth,\n  getValidDateOfYear,\n} from '../utils'\nimport { ROOT_PICKER_IS_DEFAULT_FORMAT_INJECTION_KEY } from '../constants'\nimport DateTable from './basic-date-table.vue'\nimport MonthTable from './basic-month-table.vue'\nimport YearTable from './basic-year-table.vue'\n\nimport type { SetupContext } from 'vue'\nimport type { ConfigType, Dayjs } from 'dayjs'\nimport type { PanelDatePickProps } from '../props/panel-date-pick'\nimport type {\n  DateTableEmits,\n  DatesPickerEmits,\n  MonthsPickerEmits,\n  WeekPickerEmits,\n  YearsPickerEmits,\n} from '../props/basic-date-table'\n\ntype DatePickType = PanelDatePickProps['type']\n// todo\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst timeWithinRange = (_: ConfigType, __: any, ___: string) => true\nconst props = defineProps(panelDatePickProps)\nconst contextEmit = defineEmits(['pick', 'set-picker-option', 'panel-change'])\nconst ppNs = useNamespace('picker-panel')\nconst dpNs = useNamespace('date-picker')\nconst attrs = useAttrs()\nconst slots = useSlots()\n\nconst { t, lang } = useLocale()\nconst pickerBase = inject(PICKER_BASE_INJECTION_KEY) as any\nconst isDefaultFormat = inject(\n  ROOT_PICKER_IS_DEFAULT_FORMAT_INJECTION_KEY\n) as any\nconst popper = inject(TOOLTIP_INJECTION_KEY)\nconst { shortcuts, disabledDate, cellClassName, defaultTime } = pickerBase.props\nconst defaultValue = toRef(pickerBase.props, 'defaultValue')\n\nconst currentViewRef = ref<{ focus: () => void }>()\n\nconst innerDate = ref(dayjs().locale(lang.value))\n\nconst isChangeToNow = ref(false)\n\nlet isShortcut = false\n\nconst defaultTimeD = computed(() => {\n  return dayjs(defaultTime).locale(lang.value)\n})\n\nconst month = computed(() => {\n  return innerDate.value.month()\n})\n\nconst year = computed(() => {\n  return innerDate.value.year()\n})\n\nconst selectableRange = ref([])\nconst userInputDate = ref<string | null>(null)\nconst userInputTime = ref<string | null>(null)\n// todo update to disableHour\nconst checkDateWithinRange = (date: ConfigType) => {\n  return selectableRange.value.length > 0\n    ? timeWithinRange(date, selectableRange.value, props.format || 'HH:mm:ss')\n    : true\n}\nconst formatEmit = (emitDayjs: Dayjs) => {\n  if (\n    defaultTime &&\n    !visibleTime.value &&\n    !isChangeToNow.value &&\n    !isShortcut\n  ) {\n    return defaultTimeD.value\n      .year(emitDayjs.year())\n      .month(emitDayjs.month())\n      .date(emitDayjs.date())\n  }\n  if (showTime.value) return emitDayjs.millisecond(0)\n  return emitDayjs.startOf('day')\n}\nconst emit = (value: Dayjs | Dayjs[], ...args: any[]) => {\n  if (!value) {\n    contextEmit('pick', value, ...args)\n  } else if (isArray(value)) {\n    const dates = value.map(formatEmit)\n    contextEmit('pick', dates, ...args)\n  } else {\n    contextEmit('pick', formatEmit(value), ...args)\n  }\n  userInputDate.value = null\n  userInputTime.value = null\n  isChangeToNow.value = false\n  isShortcut = false\n}\nconst handleDatePick = async (value: DateTableEmits, keepOpen?: boolean) => {\n  if (selectionMode.value === 'date') {\n    value = value as Dayjs\n    let newDate = props.parsedValue\n      ? (props.parsedValue as Dayjs)\n          .year(value.year())\n          .month(value.month())\n          .date(value.date())\n      : value\n    // change default time while out of selectableRange\n    if (!checkDateWithinRange(newDate)) {\n      newDate = (selectableRange.value[0][0] as Dayjs)\n        .year(value.year())\n        .month(value.month())\n        .date(value.date())\n    }\n    innerDate.value = newDate\n    emit(newDate, showTime.value || keepOpen)\n  } else if (selectionMode.value === 'week') {\n    emit((value as WeekPickerEmits).date)\n  } else if (selectionMode.value === 'dates') {\n    emit(value as DatesPickerEmits, true) // set true to keep panel open\n  }\n}\n\nconst moveByMonth = (forward: boolean) => {\n  const action = forward ? 'add' : 'subtract'\n  innerDate.value = innerDate.value[action](1, 'month')\n  handlePanelChange('month')\n}\n\nconst moveByYear = (forward: boolean) => {\n  const currentDate = innerDate.value\n  const action = forward ? 'add' : 'subtract'\n\n  innerDate.value =\n    currentView.value === 'year'\n      ? currentDate[action](10, 'year')\n      : currentDate[action](1, 'year')\n\n  handlePanelChange('year')\n}\n\nconst currentView = ref('date')\n\nconst yearLabel = computed(() => {\n  const yearTranslation = t('el.datepicker.year')\n  if (currentView.value === 'year') {\n    const startYear = Math.floor(year.value / 10) * 10\n    if (yearTranslation) {\n      return `${startYear} ${yearTranslation} - ${\n        startYear + 9\n      } ${yearTranslation}`\n    }\n    return `${startYear} - ${startYear + 9}`\n  }\n  return `${year.value} ${yearTranslation}`\n})\n\ntype Shortcut = {\n  value: (() => Dayjs) | Dayjs\n  onClick?: (ctx: Omit<SetupContext, 'expose'>) => void\n}\n\nconst handleShortcutClick = (shortcut: Shortcut) => {\n  const shortcutValue = isFunction(shortcut.value)\n    ? shortcut.value()\n    : shortcut.value\n  if (shortcutValue) {\n    isShortcut = true\n    emit(dayjs(shortcutValue).locale(lang.value))\n    return\n  }\n  if (shortcut.onClick) {\n    shortcut.onClick({\n      attrs,\n      slots,\n      emit: contextEmit as SetupContext['emit'],\n    })\n  }\n}\n\nconst selectionMode = computed<DatePickType>(() => {\n  const { type } = props\n  if (['week', 'month', 'months', 'year', 'years', 'dates'].includes(type))\n    return type\n  return 'date' as DatePickType\n})\n\nconst isMultipleType = computed(() => {\n  return (\n    selectionMode.value === 'dates' ||\n    selectionMode.value === 'months' ||\n    selectionMode.value === 'years'\n  )\n})\n\nconst keyboardMode = computed<string>(() => {\n  return selectionMode.value === 'date'\n    ? currentView.value\n    : selectionMode.value\n})\n\nconst hasShortcuts = computed(() => !!shortcuts.length)\n\nconst handleMonthPick = async (\n  month: number | MonthsPickerEmits,\n  keepOpen?: boolean\n) => {\n  if (selectionMode.value === 'month') {\n    innerDate.value = getValidDateOfMonth(\n      innerDate.value,\n      innerDate.value.year(),\n      month as number,\n      lang.value,\n      disabledDate\n    )\n    emit(innerDate.value, false)\n  } else if (selectionMode.value === 'months') {\n    emit(month as MonthsPickerEmits, keepOpen ?? true)\n  } else {\n    innerDate.value = getValidDateOfMonth(\n      innerDate.value,\n      innerDate.value.year(),\n      month as number,\n      lang.value,\n      disabledDate\n    )\n    currentView.value = 'date'\n    if (['month', 'year', 'date', 'week'].includes(selectionMode.value)) {\n      emit(innerDate.value, true)\n      await nextTick()\n      handleFocusPicker()\n    }\n  }\n  handlePanelChange('month')\n}\n\nconst handleYearPick = async (\n  year: number | YearsPickerEmits,\n  keepOpen?: boolean\n) => {\n  if (selectionMode.value === 'year') {\n    const data = innerDate.value.startOf('year').year(year as number)\n    innerDate.value = getValidDateOfYear(data, lang.value, disabledDate)\n    emit(innerDate.value, false)\n  } else if (selectionMode.value === 'years') {\n    emit(year as YearsPickerEmits, keepOpen ?? true)\n  } else {\n    const data = innerDate.value.year(year as number)\n    innerDate.value = getValidDateOfYear(data, lang.value, disabledDate)\n    currentView.value = 'month'\n    if (['month', 'year', 'date', 'week'].includes(selectionMode.value)) {\n      emit(innerDate.value, true)\n      await nextTick()\n      handleFocusPicker()\n    }\n  }\n  handlePanelChange('year')\n}\n\nconst showPicker = async (view: 'month' | 'year') => {\n  currentView.value = view\n  await nextTick()\n  handleFocusPicker()\n}\n\nconst showTime = computed(\n  () => props.type === 'datetime' || props.type === 'datetimerange'\n)\n\nconst footerVisible = computed(() => {\n  const showDateFooter = showTime.value || selectionMode.value === 'dates'\n  const showYearFooter = selectionMode.value === 'years'\n  const showMonthFooter = selectionMode.value === 'months'\n  const isDateView = currentView.value === 'date'\n  const isYearView = currentView.value === 'year'\n  const isMonthView = currentView.value === 'month'\n  return (\n    (showDateFooter && isDateView) ||\n    (showYearFooter && isYearView) ||\n    (showMonthFooter && isMonthView)\n  )\n})\n\nconst disabledConfirm = computed(() => {\n  if (!disabledDate) return false\n  if (!props.parsedValue) return true\n  if (isArray(props.parsedValue)) {\n    return disabledDate(props.parsedValue[0].toDate())\n  }\n  return disabledDate(props.parsedValue.toDate())\n})\nconst onConfirm = () => {\n  if (isMultipleType.value) {\n    emit(props.parsedValue as Dayjs[])\n  } else {\n    // deal with the scenario where: user opens the date time picker, then confirm without doing anything\n    let result = props.parsedValue as Dayjs\n    if (!result) {\n      const defaultTimeD = dayjs(defaultTime).locale(lang.value)\n      const defaultValueD = getDefaultValue()\n      result = defaultTimeD\n        .year(defaultValueD.year())\n        .month(defaultValueD.month())\n        .date(defaultValueD.date())\n    }\n    innerDate.value = result\n    emit(result)\n  }\n}\n\nconst disabledNow = computed(() => {\n  if (!disabledDate) return false\n  return disabledDate(dayjs().locale(lang.value).toDate())\n})\nconst changeToNow = () => {\n  // NOTE: not a permanent solution\n  //       consider disable \"now\" button in the future\n  const now = dayjs().locale(lang.value)\n  const nowDate = now.toDate()\n  isChangeToNow.value = true\n  if (\n    (!disabledDate || !disabledDate(nowDate)) &&\n    checkDateWithinRange(nowDate)\n  ) {\n    innerDate.value = dayjs().locale(lang.value)\n    emit(innerDate.value)\n  }\n}\n\nconst timeFormat = computed(() => {\n  return props.timeFormat || extractTimeFormat(props.format)\n})\n\nconst dateFormat = computed(() => {\n  return props.dateFormat || extractDateFormat(props.format)\n})\n\nconst visibleTime = computed(() => {\n  if (userInputTime.value) return userInputTime.value\n  if (!props.parsedValue && !defaultValue.value) return\n  return ((props.parsedValue || innerDate.value) as Dayjs).format(\n    timeFormat.value\n  )\n})\n\nconst visibleDate = computed(() => {\n  if (userInputDate.value) return userInputDate.value\n  if (!props.parsedValue && !defaultValue.value) return\n  return ((props.parsedValue || innerDate.value) as Dayjs).format(\n    dateFormat.value\n  )\n})\n\nconst timePickerVisible = ref(false)\nconst onTimePickerInputFocus = () => {\n  timePickerVisible.value = true\n}\nconst handleTimePickClose = () => {\n  timePickerVisible.value = false\n}\n\nconst getUnits = (date: Dayjs) => {\n  return {\n    hour: date.hour(),\n    minute: date.minute(),\n    second: date.second(),\n    year: date.year(),\n    month: date.month(),\n    date: date.date(),\n  }\n}\n\nconst handleTimePick = (value: Dayjs, visible: boolean, first: boolean) => {\n  const { hour, minute, second } = getUnits(value)\n  const newDate = props.parsedValue\n    ? (props.parsedValue as Dayjs).hour(hour).minute(minute).second(second)\n    : value\n  innerDate.value = newDate\n  emit(innerDate.value, true)\n  if (!first) {\n    timePickerVisible.value = visible\n  }\n}\n\nconst handleVisibleTimeChange = (value: string) => {\n  const newDate = dayjs(value, timeFormat.value).locale(lang.value)\n  if (newDate.isValid() && checkDateWithinRange(newDate)) {\n    const { year, month, date } = getUnits(innerDate.value)\n    innerDate.value = newDate.year(year).month(month).date(date)\n    userInputTime.value = null\n    timePickerVisible.value = false\n    emit(innerDate.value, true)\n  }\n}\n\nconst handleVisibleDateChange = (value: string) => {\n  const newDate = correctlyParseUserInput(\n    value,\n    dateFormat.value,\n    lang.value,\n    isDefaultFormat\n  ) as Dayjs\n  if (newDate.isValid()) {\n    if (disabledDate && disabledDate(newDate.toDate())) {\n      return\n    }\n    const { hour, minute, second } = getUnits(innerDate.value)\n    innerDate.value = newDate.hour(hour).minute(minute).second(second)\n    userInputDate.value = null\n    emit(innerDate.value, true)\n  }\n}\n\nconst isValidValue = (date: unknown) => {\n  return (\n    dayjs.isDayjs(date) &&\n    date.isValid() &&\n    (disabledDate ? !disabledDate(date.toDate()) : true)\n  )\n}\n\nconst formatToString = (value: Dayjs | Dayjs[]) => {\n  return isArray(value)\n    ? (value as Dayjs[]).map((_) => _.format(props.format))\n    : (value as Dayjs).format(props.format)\n}\n\nconst parseUserInput = (value: Dayjs) => {\n  return correctlyParseUserInput(\n    value,\n    props.format,\n    lang.value,\n    isDefaultFormat\n  )\n}\n\nconst getDefaultValue = () => {\n  const parseDate = dayjs(defaultValue.value).locale(lang.value)\n  if (!defaultValue.value) {\n    const defaultTimeDValue = defaultTimeD.value\n    return dayjs()\n      .hour(defaultTimeDValue.hour())\n      .minute(defaultTimeDValue.minute())\n      .second(defaultTimeDValue.second())\n      .locale(lang.value)\n  }\n  return parseDate\n}\n\nconst handleFocusPicker = () => {\n  if (['week', 'month', 'year', 'date'].includes(selectionMode.value)) {\n    currentViewRef.value?.focus()\n  }\n}\n\nconst _handleFocusPicker = () => {\n  handleFocusPicker()\n  // TODO: After focus the date input, the first time you use the ArrowDown keys, you cannot focus on the date cell\n  if (selectionMode.value === 'week') {\n    handleKeyControl(EVENT_CODE.down)\n  }\n}\n\nconst handleKeydownTable = (event: KeyboardEvent) => {\n  const { code } = event\n  const validCode = [\n    EVENT_CODE.up,\n    EVENT_CODE.down,\n    EVENT_CODE.left,\n    EVENT_CODE.right,\n    EVENT_CODE.home,\n    EVENT_CODE.end,\n    EVENT_CODE.pageUp,\n    EVENT_CODE.pageDown,\n  ]\n  if (validCode.includes(code)) {\n    handleKeyControl(code)\n    event.stopPropagation()\n    event.preventDefault()\n  }\n  if (\n    [EVENT_CODE.enter, EVENT_CODE.space, EVENT_CODE.numpadEnter].includes(\n      code\n    ) &&\n    userInputDate.value === null &&\n    userInputTime.value === null\n  ) {\n    event.preventDefault()\n    emit(innerDate.value, false)\n  }\n}\n\nconst handleKeyControl = (code: string) => {\n  type KeyControlMappingCallableOffset = (date: Date, step?: number) => number\n  type KeyControl = {\n    [key: string]:\n      | number\n      | KeyControlMappingCallableOffset\n      | ((date: Date, step: number) => any)\n    offset: (date: Date, step: number) => any\n  }\n  interface KeyControlMapping {\n    [key: string]: KeyControl\n  }\n\n  const { up, down, left, right, home, end, pageUp, pageDown } = EVENT_CODE\n  const mapping: KeyControlMapping = {\n    year: {\n      [up]: -4,\n      [down]: 4,\n      [left]: -1,\n      [right]: 1,\n      offset: (date: Date, step: number) =>\n        date.setFullYear(date.getFullYear() + step),\n    },\n    month: {\n      [up]: -4,\n      [down]: 4,\n      [left]: -1,\n      [right]: 1,\n      offset: (date: Date, step: number) =>\n        date.setMonth(date.getMonth() + step),\n    },\n    week: {\n      [up]: -1,\n      [down]: 1,\n      [left]: -1,\n      [right]: 1,\n      offset: (date: Date, step: number) =>\n        date.setDate(date.getDate() + step * 7),\n    },\n    date: {\n      [up]: -7,\n      [down]: 7,\n      [left]: -1,\n      [right]: 1,\n      [home]: (date: Date) => -date.getDay(),\n      [end]: (date: Date) => -date.getDay() + 6,\n      [pageUp]: (date: Date) =>\n        -new Date(date.getFullYear(), date.getMonth(), 0).getDate(),\n      [pageDown]: (date: Date) =>\n        new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate(),\n      offset: (date: Date, step: number) => date.setDate(date.getDate() + step),\n    },\n  }\n\n  const newDate = innerDate.value.toDate()\n  while (Math.abs(innerDate.value.diff(newDate, 'year', true)) < 1) {\n    const map = mapping[keyboardMode.value]\n    if (!map) return\n    map.offset(\n      newDate,\n      isFunction(map[code])\n        ? (map[code] as unknown as KeyControlMappingCallableOffset)(newDate)\n        : (map[code] as number) ?? 0\n    )\n    if (disabledDate && disabledDate(newDate)) {\n      break\n    }\n    const result = dayjs(newDate).locale(lang.value)\n    innerDate.value = result\n    contextEmit('pick', result, true)\n    break\n  }\n}\n\nconst handlePanelChange = (mode: 'month' | 'year') => {\n  contextEmit('panel-change', innerDate.value.toDate(), mode, currentView.value)\n}\n\nwatch(\n  () => selectionMode.value,\n  (val) => {\n    if (['month', 'year'].includes(val)) {\n      currentView.value = val\n      return\n    } else if (val === 'years') {\n      currentView.value = 'year'\n      return\n    } else if (val === 'months') {\n      currentView.value = 'month'\n      return\n    }\n    currentView.value = 'date'\n  },\n  { immediate: true }\n)\n\nwatch(\n  () => currentView.value,\n  () => {\n    popper?.updatePopper()\n  }\n)\n\nwatch(\n  () => defaultValue.value,\n  (val) => {\n    if (val) {\n      innerDate.value = getDefaultValue()\n    }\n  },\n  { immediate: true }\n)\n\nwatch(\n  () => props.parsedValue,\n  (val) => {\n    if (val) {\n      if (isMultipleType.value) return\n      if (isArray(val)) return\n      innerDate.value = val\n    } else {\n      innerDate.value = getDefaultValue()\n    }\n  },\n  { immediate: true }\n)\n\ncontextEmit('set-picker-option', ['isValidValue', isValidValue])\ncontextEmit('set-picker-option', ['formatToString', formatToString])\ncontextEmit('set-picker-option', ['parseUserInput', parseUserInput])\ncontextEmit('set-picker-option', ['handleFocusPicker', _handleFocusPicker])\n</script>\n"], "names": ["month", "year", "defaultTimeD", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref", "_createElementVNode", "_renderSlot", "_Fragment", "_renderList", "_toDisplayString", "_createCommentVNode"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4PA,IAAA,MAAM,eAAkB,GAAA,CAAC,CAAe,EAAA,EAAA,EAAS,GAAgB,KAAA,IAAA,CAAA;AAGjE,IAAM,MAAA,IAAA,GAAO,aAAa,cAAc,CAAA,CAAA;AACxC,IAAM,MAAA,IAAA,GAAO,aAAa,aAAa,CAAA,CAAA;AACvC,IAAA,MAAM,QAAQ,QAAS,EAAA,CAAA;AACvB,IAAA,MAAM,QAAQ,QAAS,EAAA,CAAA;AAEvB,IAAA,MAAM,EAAE,CAAA,EAAG,IAAK,EAAA,GAAI,SAAU,EAAA,CAAA;AAC9B,IAAM,MAAA,UAAA,GAAa,OAAO,yBAAyB,CAAA,CAAA;AACnD,IAAA,MAAM,eAAkB,GAAA,MAAA,CAAA,2CAAA,CAAA,CAAA;AAAA,IACtB,MAAA,MAAA,GAAA,MAAA,CAAA,qBAAA,CAAA,CAAA;AAAA,IACF,MAAA,EAAA,SAAA,EAAA,YAAA,EAAA,aAAA,EAAA,WAAA,EAAA,GAAA,UAAA,CAAA,KAAA,CAAA;AACA,IAAM,MAAA,oBAAqC,CAAA,UAAA,CAAA,KAAA,EAAA,cAAA,CAAA,CAAA;AAC3C,IAAA,MAAM,cAAa,GAAA,GAAA,EAAA,CAAA;AACnB,IAAA,MAAM,SAAe,GAAA,GAAA,CAAA,KAAM,EAAW,CAAA,MAAA,CAAA,IAAA,CAAA,KAAqB,CAAA,CAAA,CAAA;AAE3D,IAAA,MAAM,mBAA4C,CAAA,KAAA,CAAA,CAAA;AAElD,IAAA,IAAA,kBAAsB,CAAA;AAEtB,IAAM,MAAA,YAAA,GAAA,QAAyB,CAAA,MAAA;AAE/B,MAAA,OAAiB,KAAA,CAAA,WAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAEjB,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,KAAa,GAAA,QAAA,CAAA,MAAa;AAAiB,MAC5C,OAAA,SAAA,CAAA,KAAA,CAAA,KAAA,EAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAO,MAAA,IAAA,GAAA,eAAsB;AAAA,MAC9B,OAAA,SAAA,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAO,MAAA,qBAAqB,CAAA,EAAA,CAAA,CAAA;AAAA,IAC9B,MAAC,aAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA;AAED,IAAM,MAAA,aAAA,GAAA,GAAsB,CAAA,IAAE,CAAA,CAAA;AAC9B,IAAM,MAAA,oBAAmC,GAAI,CAAA,IAAA,KAAA;AAC7C,MAAM,OAAA,qBAAuC,CAAA,MAAA,GAAA,CAAA,GAAA,eAAA,CAAA,IAAA,EAAA,eAAA,CAAA,KAAA,EAAA,KAAA,CAAA,MAAA,IAAA,UAAA,CAAA,GAAA,IAAA,CAAA;AAE7C,KAAM,CAAA;AACJ,IAAO,MAAA,UAAA,GAAA,CAAA,SAAsB,KAAA;AAEzB,MACN,IAAA,WAAA,IAAA,CAAA,WAAA,CAAA,KAAA,IAAA,CAAA,aAAA,CAAA,KAAA,IAAA,CAAA,UAAA,EAAA;AACA,QAAM,OAAA,YAAmC,CAAA,KAAA,CAAA,IAAA,CAAA,SAAA,CAAA,IAAA,EAAA,CAAA,CAAA,KAAA,CAAA,SAAA,CAAA,KAAA,EAAA,CAAA,CAAA,IAAA,CAAA,SAAA,CAAA,IAAA,EAAA,CAAA,CAAA;AACvC,OACE;AAKA,MAAA,IAAA,QAAoB,CAAA,KAAA;AAGI,QAC1B,OAAA,SAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA;AACA,MAAA,OAAa,SAAA,CAAA,OAAc,CAAA,KAAA,CAAA,CAAA;AAC3B,KAAO,CAAA;AAAuB,IAChC,MAAA,IAAA,GAAA,CAAA,KAAA,EAAA,GAAA,IAAA,KAAA;AACA,MAAM,IAAA,CAAA,KAAA,EAAQ;AACZ,QAAA,WAAY,CAAA,MAAA,EAAA,KAAA,EAAA,GAAA,IAAA,CAAA,CAAA;AACV,OAAY,MAAA,IAAA,OAAA,CAAA,KAAe,CAAA,EAAA;AAAO,QACpC,MAAA,KAAmB,GAAA,KAAA,CAAA,GAAQ,CAAA,UAAA,CAAA,CAAA;AACzB,QAAM,WAAA,CAAA,MAAc,EAAA,KAAc,EAAA,GAAA,IAAA,CAAA,CAAA;AAClC,OAAY,MAAA;AAAsB,QAC7B,WAAA,CAAA,MAAA,EAAA,UAAA,CAAA,KAAA,CAAA,EAAA,GAAA,IAAA,CAAA,CAAA;AACL,OAAA;AAA8C,MAChD,aAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACA,MAAA,aAAA,CAAc,KAAQ,GAAA,IAAA,CAAA;AACtB,MAAA,aAAA,CAAc,KAAQ,GAAA,KAAA,CAAA;AACtB,MAAA,UAAA,GAAA,KAAsB,CAAA;AACtB,KAAa,CAAA;AAAA,IACf,MAAA,cAAA,GAAA,OAAA,KAAA,EAAA,QAAA,KAAA;AACA,MAAM,IAAA,aAAA,CAAA,KAAwB,KAAA,MAAA,EAA8C;AAC1E,QAAI,KAAA,GAAA,KAAA,CAAA;AACF,QAAQ,IAAA,OAAA,GAAA,KAAA,CAAA,WAAA,GAAA,KAAA,CAAA,WAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA,CAAA,KAAA,CAAA,KAAA,CAAA,KAAA,EAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA,GAAA,KAAA,CAAA;AACR,QAAA,IAAI,qBAAgB,CAAA,OAAA,CACf;AAML,UAAI,OAAsB,GAAA,eAAA,CAAA,KAAO,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA,CAAA,KAAA,CAAA,KAAA,CAAA,KAAA,EAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA,CAAA;AAClC,SAAA;AAGoB,QACtB,SAAA,CAAA,KAAA,GAAA,OAAA,CAAA;AACA,QAAA,IAAA,CAAA,OAAkB,EAAA,QAAA,CAAA,KAAA,IAAA,QAAA,CAAA,CAAA;AAClB,OAAK,MAAA,IAAA,aAAkB,CAAA,KAAA,KAAiB,MAAA,EAAA;AAAA,QAC1C,IAAA,CAAA,KAAyB,CAAA,IAAA,CAAA,CAAA;AACvB,OAAA,MAAM,iBAA8B,CAAA,KAAA,KAAA,OAAA,EAAA;AAAA,QACtC,IAAA,CAAA,KAAyB,EAAA,IAAA,CAAA,CAAA;AACvB,OAAA;AAAoC,KACtC,CAAA;AAAA,IACF,MAAA,WAAA,GAAA,CAAA,OAAA,KAAA;AAEA,MAAM,MAAA,MAAA,GAAA,OAAoC,GAAA,KAAA,GAAA,UAAA,CAAA;AACxC,MAAM,SAAA,CAAA,KAAS,YAAkB,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA,EAAA,OAAA,CAAA,CAAA;AACjC,MAAA,kBAAkB,OAAU,CAAA,CAAA;AAC5B,KAAA,CAAA;AAAyB,IAC3B,MAAA,UAAA,GAAA,CAAA,OAAA,KAAA;AAEA,MAAM,MAAA,WAAa,GAAsB,SAAA,CAAA,KAAA,CAAA;AACvC,MAAA,MAAM,gBAAwB,GAAA,KAAA,GAAA,UAAA,CAAA;AAC9B,MAAM,SAAA,CAAA,KAAS,cAAkB,CAAA,KAAA,KAAA,MAAA,GAAA,WAAA,CAAA,MAAA,CAAA,CAAA,EAAA,EAAA,MAAA,CAAA,GAAA,WAAA,CAAA,MAAA,CAAA,CAAA,CAAA,EAAA,MAAA,CAAA,CAAA;AAEjC,MAAA,iBACE,CAAA,MAAA,CAAA,CAAA;AAIF,KAAA,CAAA;AAAwB,IAC1B,MAAA,WAAA,GAAA,GAAA,CAAA,MAAA,CAAA,CAAA;AAEA,IAAM,MAAA,SAAA,GAAA,QAAwB,CAAA,MAAA;AAE9B,MAAM,MAAA,mBAAqB,CAAM,oBAAA,CAAA,CAAA;AAC/B,MAAM,IAAA,WAAA,CAAA,KAAA,KAAoB,MAAoB,EAAA;AAC9C,QAAI,MAAA,sBAA8B,CAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,GAAA,EAAA,CAAA;AAChC,QAAA,IAAA,eAAuB,EAAA;AACvB,UAAA,OAAqB,CAAA,EAAA,SAAA,CAAA,CAAA,EAAA,eAAA,CAAA,GAAA,EAAA,SAAA,GAAA,CAAA,CAAA,CAAA,EAAA,eAAA,CAAA,CAAA,CAAA;AACnB,SAAO;AAEY,QACrB,OAAA,CAAA,EAAA,SAAA,CAAA,GAAA,EAAA,SAAA,GAAA,CAAA,CAAA,CAAA,CAAA;AACA,OAAA;AAAsC,MACxC,OAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA,EAAA,eAAA,CAAA,CAAA,CAAA;AACA,KAAA,CAAA,CAAA;AAAuC,IACzC,MAAC,mBAAA,GAAA,CAAA,QAAA,KAAA;AAOD,MAAM,MAAA,aAAA,GAAA,UAA8C,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,QAAA,CAAA,KAAA,EAAA,GAAA,QAAA,CAAA,KAAA,CAAA;AAClD,MAAM,IAAA,aAAA,EAAA;AAGN,QAAA,UAAmB,GAAA,IAAA,CAAA;AACjB,QAAa,IAAA,CAAA,KAAA,CAAA,aAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACb,QAAA;AACA,OAAA;AAAA,MACF,IAAA,QAAA,CAAA,OAAA,EAAA;AACA,QAAA,gBAAsB,CAAA;AACpB,UAAA,KAAA;AAAiB,UACf,KAAA;AAAA,UACA,IAAA,EAAA,WAAA;AAAA,SAAA,CACA,CAAM;AAAA,OAAA;AACP,KACH,CAAA;AAAA,IACF,MAAA,aAAA,GAAA,QAAA,CAAA,MAAA;AAEA,MAAM,MAAA,EAAA,IAAA,EAAA,GAAA;AACJ,MAAM,IAAA,CAAA,QAAO,OAAI,EAAA,QAAA,EAAA,MAAA,EAAA,OAAA,EAAA,OAAA,CAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACjB,QAAI,WAAS,CAAS;AACpB,MAAO,OAAA,MAAA,CAAA;AACT,KAAO,CAAA,CAAA;AAAA,IACT,MAAC,cAAA,GAAA,QAAA,CAAA,MAAA;AAED,MAAM,OAAA,aAAA,CAAiB,UAAe,OAAA,IAAA,aAAA,CAAA,KAAA,KAAA,QAAA,IAAA,aAAA,CAAA,KAAA,KAAA,OAAA,CAAA;AACpC,KAAA,CAAA,CAAA;AAG0B,IAE5B,MAAC,YAAA,GAAA,QAAA,CAAA,MAAA;AAED,MAAM,OAAA,wBAAsC,MAAA,GAAA,WAAA,CAAA,KAAA,GAAA,aAAA,CAAA,KAAA,CAAA;AAC1C,KAAA,CAAA,CAAA;AAEkB,IACpB,MAAC,YAAA,GAAA,QAAA,CAAA,MAAA,CAAA,CAAA,SAAA,CAAA,MAAA,CAAA,CAAA;AAED,IAAA,MAAM,eAAe,GAAS,OAAA,MAAO,eAAiB;AAEtD,MAAM,IAAA,aAAA,CAAA,KACJA,KAAAA,OAAAA,EACA;AAEA,QAAI,SAAA,CAAA,KAAA,sBAAiC,CAAA,SAAA,CAAA,KAAA,EAAA,SAAA,CAAA,KAAA,CAAA,IAAA,EAAA,EAAA,MAAA,EAAA,IAAA,CAAA,KAAA,EAAA,YAAA,CAAA,CAAA;AACnC,QAAA,IAAA,CAAA,SAAkB,CAAA,KAAA,EAAA,KAAA,CAAA,CAAA;AAAA,OAAA,MACN,IAAA,aAAA,CAAA,KAAA,KAAA,QAAA,EAAA;AAAA,QACV,IAAA,CAAA,MAAA,UAAqB,IAAA,IAAA,GAAA,QAAA,GAAA,IAAA,CAAA,CAAA;AAAA,OACrBA,MAAAA;AAAA,QAAA,SACK,CAAA,KAAA,GAAA,mBAAA,CAAA,SAAA,CAAA,KAAA,EAAA,SAAA,CAAA,KAAA,CAAA,IAAA,EAAA,EAAA,MAAA,EAAA,IAAA,CAAA,KAAA,EAAA,YAAA,CAAA,CAAA;AAAA,QACL,WAAA,CAAA,KAAA,GAAA,MAAA,CAAA;AAAA,QACF,IAAA,CAAA,OAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,CAAA,CAAA,QAAA,CAAA,aAAA,CAAA,KAAA,CAAA,EAAA;AACA,UAAK,IAAA,CAAA,eAAiB,EAAK,IAAA,CAAA,CAAA;AAAA,UAC7B,MAAA,QAAyB,EAAA,CAAA;AACvB,UAAKA;AAA4C,SAC5C;AACL,OAAA;AAAkB,MAAA,iBACN,CAAA,OAAA,CAAA,CAAA;AAAA,KACV,CAAA;AAAqB,IACrBA,MAAAA,cAAAA,GAAAA,OAAAA,KAAAA,EAAAA,QAAAA,KAAAA;AAAA,MAAA,IACA,aAAK,CAAA,KAAA,KAAA,MAAA,EAAA;AAAA,QACL,MAAA,IAAA,GAAA,SAAA,CAAA,KAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,QACF,SAAA,CAAA,KAAA,GAAA,kBAAA,CAAA,IAAA,EAAA,IAAA,CAAA,KAAA,EAAA,YAAA,CAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAoB,CAAA,KAAA,EAAA,KAAA,CAAA,CAAA;AACpB,OAAI,MAAC,iBAAiB,CAAA,KAAA,YAAgB,EAAA;AACpC,QAAK,IAAA,CAAA,KAAA,EAAA,YAAiB,IAAI,GAAA,QAAA,GAAA,IAAA,CAAA,CAAA;AAC1B,OAAA,MAAA;AACA,QAAkB,MAAA,IAAA,GAAA,SAAA,CAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,QACpB,SAAA,CAAA,KAAA,GAAA,kBAAA,CAAA,IAAA,EAAA,IAAA,CAAA,KAAA,EAAA,YAAA,CAAA,CAAA;AAAA,QACF,WAAA,CAAA,KAAA,GAAA,OAAA,CAAA;AACA,QAAA,IAAA,CAAA,OAAA,EAAA,MAAyB,EAAA,MAAA,EAAA,MAAA,CAAA,CAAA,QAAA,CAAA,aAAA,CAAA,KAAA,CAAA,EAAA;AAAA,UAC3B,IAAA,CAAA,SAAA,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AAEA,UAAM,MAAA,QAAA,EAAA,CAAiB;AAIrB,UAAI;AACF,SAAA;AACA,OAAA;AACA,MAAK,iBAAU,OAAO,CAAK,CAAA;AAAA,KAC7B,CAAA;AACE,IAAKC,MAAAA,UAA0B,cAAgB,KAAA;AAAA,MACjD,WAAO,CAAA,KAAA,GAAA,IAAA,CAAA;AACL,MAAA,MAAA,QAAa,EAAA,CAAA;AACb,MAAA,iBAAkB,EAAA,CAAA;AAClB,KAAA,CAAA;AACA,IAAI,MAAA,mBAAkB,CAAA,MAAA,UAAgB,KAAA,UAAuB,IAAA,KAAA,CAAA,IAAQ,KAAA,eAAA,CAAA,CAAA;AACnE,IAAK,MAAA,aAAA,WAAqB,CAAA,MAAA;AAC1B,MAAA,MAAA,cAAe,GAAA,QAAA,CAAA,KAAA,IAAA,aAAA,CAAA,KAAA,KAAA,OAAA,CAAA;AACf,MAAkB,MAAA,cAAA,GAAA,aAAA,CAAA,KAAA,KAAA,OAAA,CAAA;AAAA,MACpB,MAAA,eAAA,GAAA,aAAA,CAAA,KAAA,KAAA,QAAA,CAAA;AAAA,MACF,MAAA,UAAA,GAAA,WAAA,CAAA,KAAA,KAAA,MAAA,CAAA;AACA,MAAA,MAAA,UAAA,GAAwB,WAAA,CAAA,KAAA,KAAA,MAAA,CAAA;AAAA,MAC1B,MAAA,WAAA,GAAA,WAAA,CAAA,KAAA,KAAA,OAAA,CAAA;AAEA,MAAM,OAAA,kBAA+C,UAAA,IAAA,cAAA,IAAA,UAAA,IAAA,eAAA,IAAA,WAAA,CAAA;AACnD,KAAA,CAAA,CAAA;AACA,IAAA,MAAA,eAAe,GAAA,QAAA,CAAA,MAAA;AACf,MAAkB,IAAA,CAAA,YAAA;AAAA,QACpB,OAAA,KAAA,CAAA;AAEA,MAAA,IAAM,CAAW,KAAA,CAAA,WAAA;AAAA,QACT,OAAA,IAAe,CAAA;AAA6B,MACpD,IAAA,OAAA,CAAA,KAAA,CAAA,WAAA,CAAA,EAAA;AAEA,QAAM,OAAA,8BAA+B,CAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA;AACnC,OAAA;AACA,MAAM,OAAA,YAAA,CAAA,iBAA+B,CAAU,MAAA,EAAA,CAAA,CAAA;AAC/C,KAAM,CAAA,CAAA;AACN,IAAM,MAAA,SAAA,GAAA;AACN,MAAM,IAAA,cAAA,CAAa;AACnB,QAAM,IAAA,CAAA,KAAA,CAAA;AACN,OAAA,MACG;AAEmB,QAEvB,IAAA,MAAA,GAAA,KAAA,CAAA,WAAA,CAAA;AAED,QAAM,IAAA,CAAA,MAAA,EAAA;AACJ,UAAI,mBAAsB,GAAA,KAAA,CAAA,WAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAC1B,UAAI,MAAO,aAAa,GAAO,eAAA,EAAA,CAAA;AAC/B,UAAI,MAAA,GAAc,aAAA,CAAA,IAAc,CAAA,aAAA,CAAA,IAAA,EAAA,CAAA,CAAA,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA,CAAA,CAAA,IAAA,CAAA,aAAA,CAAA,IAAA,EAAA,CAAA,CAAA;AAC9B,SAAA;AAAiD,QACnD,SAAA,CAAA,KAAA,GAAA,MAAA,CAAA;AACA,QAAA,IAAA,CAAO,MAAa,CAAA,CAAA;AAA0B,OAC/C;AACD,KAAA,CAAA;AACE,IAAA,MAAI,sBAAsB,CAAA,MAAA;AACxB,MAAA,IAAA,CAAA,YAAiC;AAAA,QAC5B,OAAA,KAAA,CAAA;AAEL,MAAA,mBAAmB,CAAA,KAAA,EAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA;AACnB,KAAA,CAAA,CAAA;AACE,IAAA,MAAA;AACA,MAAA,MAAA,GAAA,iBAAsB,CAAgB,IAAA,CAAA,KAAA,CAAA,CAAA;AACtC,MAAA,MAAA,OAASC,GACN,GAAA,CAAA,MAAA,EAAA,CAAA;AAEyB,MAC9B,aAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACA,MAAA,IAAA,CAAA,CAAA,YAAkB,IAAA,CAAA,YAAA,CAAA,OAAA,CAAA,KAAA,oBAAA,CAAA,OAAA,CAAA,EAAA;AAClB,QAAA,SAAW,CAAA,KAAA,GAAA,KAAA,EAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,QACb,IAAA,CAAA,SAAA,CAAA,KAAA,CAAA,CAAA;AAAA,OACF;AAEA,KAAM,CAAA;AACJ,IAAI,MAAA,qBAAsB,CAAA,MAAA;AAC1B,MAAO,OAAA,KAAA,CAAA,cAAqB,iBAAiB,CAAA,YAAU,CAAA,CAAA;AAAA,KACxD,CAAA,CAAA;AACD,IAAA,MAAM,qBAAoB,CAAA,MAAA;AAGxB,MAAA,OAAY,KAAA,CAAA,UAAQ,IAAO,iBAAU,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AACrC,KAAM,CAAA,CAAA;AACN,IAAA,MAAA,WAAsB,GAAA,QAAA,CAAA,MAAA;AACtB,MACG,IAAA,mBAAkB;AAGnB,QAAA,OAAA,aAAkB,CAAA,KAAQ,CAAA;AAC1B,MAAA,IAAA,CAAA,iBAAoB,IAAA,CAAA,YAAA,CAAA,KAAA;AAAA,QACtB,OAAA;AAAA,MACF,OAAA,CAAA,KAAA,CAAA,WAAA,IAAA,SAAA,CAAA,KAAA,EAAA,MAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA;AAEA,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,WAAa,GAAA,QAAA,CAAc,MAAkB;AAAY,MAC1D,IAAA,aAAA,CAAA,KAAA;AAED,QAAM,OAAA,mBAA4B,CAAA;AAChC,MAAA,IAAA,CAAA,KAAa,CAAA,WAAA,IAAA,CAAc,YAAkB,CAAA,KAAA;AAAY,QAC1D,OAAA;AAED,MAAM,OAAA,CAAA,KAAA,CAAA,eAA6B,SAAA,CAAA,KAAA,EAAA,MAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA;AACjC,KAAI,CAAA,CAAA;AACJ,IAAA,MAAI,iBAAsB,GAAA,GAAC;AAC3B,IAAS,MAAA,sBAAqB,GAAA,MAAA;AAA2B,MAAA,iBAC5C,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,KACb,CAAA;AAAA,IACF,MAAC,mBAAA,GAAA,MAAA;AAED,MAAM,iBAAA,CAAc,aAAe,CAAA;AACjC,KAAI,CAAA;AACJ,IAAA,MAAI,QAAsB,GAAA,CAAA,IAAA,KAAA;AAC1B,MAAS,OAAA;AAAgD,QACvD,IAAW,EAAA,IAAA,CAAA,IAAA,EAAA;AAAA,QACb,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA;AAAA,QACD,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA;AAED,QAAM,IAAA,EAAA,IAAA,CAAA,IAAA,EAAA;AACN,QAAA;AACE,QAAA,IAAA,EAAA,IAAA,CAAA,IAAA,EAA0B;AAAA,OAC5B,CAAA;AACA,KAAA,CAAA;AACE,IAAA,MAAA,cAAkB,GAAQ,CAAA,KAAA,EAAA,OAAA,EAAA,KAAA,KAAA;AAAA,MAC5B,MAAA,EAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,GAAA,QAAA,CAAA,KAAA,CAAA,CAAA;AAEA,MAAM,MAAA,OAAA,GAAY,KAAgB,CAAA,WAAA,GAAA,KAAA,CAAA,WAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,KAAA,CAAA;AAChC,MAAO,SAAA,CAAA,KAAA,GAAA,OAAA,CAAA;AAAA,MACL,IAAA,CAAA,SAAgB,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AAAA,MAChB,IAAA,CAAA,KAAQ;AAAY,QACpB,iBAAoB,CAAA,KAAA,GAAA,OAAA,CAAA;AAAA,OACpB;AAAgB,KAChB,CAAA;AAAkB,IAClB,MAAA,uBAAgB,GAAA,CAAA,KAAA,KAAA;AAAA,MAClB,MAAA,OAAA,GAAA,KAAA,CAAA,KAAA,EAAA,UAAA,CAAA,KAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,MACF,IAAA,OAAA,CAAA,OAAA,EAAA,IAAA,oBAAA,CAAA,OAAA,CAAA,EAAA;AAEA,QAAA,MAAuB,EAAA,IAAA,EAAA,KAAA,EAAe,KAAA,EAAA,MAAA,EAAkB,IAAmB,EAAA,GAAA,QAAA,CAAA,SAAA,CAAA,KAAA,CAAA,CAAA;AACzE,QAAA,SAAc,CAAA,KAAA,GAAA,OAAe,CAAA,IAAA,CAAI,YAAc,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;AAC/C,QAAA,aAAgB,CAAA,KAAA,GACX,IAAA,CAAA;AAEL,QAAA,iBAAkB,CAAA,KAAA,GAAA,KAAA,CAAA;AAClB,QAAK,IAAA,CAAA,eAAiB,EAAI,IAAA,CAAA,CAAA;AAC1B,OAAA;AACE,KAAA,CAAA;AAA0B,IAC5B,MAAA,uBAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MACF,MAAA,OAAA,GAAA,uBAAA,CAAA,KAAA,EAAA,UAAA,CAAA,KAAA,EAAA,IAAA,CAAA,KAAA,EAAA,eAAA,CAAA,CAAA;AAEA,MAAM,IAAA,OAAA,CAAA,OAAA,EAAA,EAAA;AACJ,QAAM,IAAA,gBAAgB,YAAO,CAAA,cAAkB,EAAA,CAAA,EAAO;AACtD,UAAI,OAAQ;AACV,SAAM;AACN,QAAU,MAAA,EAAA,IAAA,EAAA,cAAqBD,EAAAA,GAAAA,QAAYD,CAAAA,SAAO,CAAA,KAAK,CAAI,CAAA;AAC3D,QAAA,SAAA,CAAA,KAAsB,GAAA,OAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA;AACtB,QAAA,aAAA,CAAA,KAA0B,GAAA,IAAA,CAAA;AAC1B,QAAK,IAAA,CAAA,SAAA,CAAU,OAAO,IAAI,CAAA,CAAA;AAAA,OAC5B;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,YAAA,GAAA,CAAA,IAAA,KAAA;AACJ,MAAA,OAAgB,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,OAAA,EAAA,KAAA,YAAA,GAAA,CAAA,YAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AAAA,KACd,CAAA;AAAA,IAAA,MACW,cAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MAAA,OACN,OAAA,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,GAAA,KAAA,CAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AAAA,KACL,CAAA;AAAA,IACF,MAAA,cAAA,GAAA,CAAA,KAAA,KAAA;AACA,MAAI,OAAA,uBAAmB,CAAA,KAAA,EAAA,KAAA,CAAA,MAAA,EAAA,IAAA,CAAA,KAAA,EAAA,eAAA,CAAA,CAAA;AACrB,KAAA,CAAA;AACE,IAAA,MAAA,eAAA,GAAA,MAAA;AAAA,MACF,MAAA,SAAA,GAAA,KAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACA,MAAA,IAAA,CAAA,YAAc,CAAA,KAAA;AACd,QAAU,MAAA,oBAAgB,YAAkB,CAAA,KAAA,CAAA;AAC5C,QAAA,OAAA,KAAA,EAAc,CAAQ,IAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,MAAA,CAAA,iBAAA,CAAA,MAAA,EAAA,CAAA,CAAA,MAAA,CAAA,iBAAA,CAAA,MAAA,EAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACtB,OAAK;AAAqB,MAC5B,OAAA,SAAA,CAAA;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,iBAAkC,GAAA,MAAA;AACtC,MAAA,IAAA,EAAA,CACE;AAE+C,MAEnD,IAAA,CAAA,MAAA,EAAA,OAAA,EAAA,MAAA,EAAA,MAAA,CAAA,CAAA,QAAA,CAAA,aAAA,CAAA,KAAA,CAAA,EAAA;AAEA,QAAM,CAAA,EAAA,GAAA,cAAkB,CAA2B,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,EAAA,CAAA;AACjD,OAAA;AAEwC,KAC1C,CAAA;AAEA,IAAM,MAAA,kBAAkB,GAAiB,MAAA;AACvC,MAAO,iBAAA,EAAA,CAAA;AAAA,MACL,IAAA,aAAA,CAAA,KAAA,KAAA,MAAA,EAAA;AAAA,QACA,gBAAM,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;AAAA,OAAA;AACD,KACL,CAAA;AAAA,IACF,MAAA,kBAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MACF,MAAA,EAAA,IAAA,EAAA,GAAA,KAAA,CAAA;AAEA,MAAA;AACE,QAAA;AACA,QAAI;AACF,QAAA;AACA,QAAA,gBACG;AAGiB,QACtB,UAAA,CAAA,IAAA;AACA,QAAO,UAAA,CAAA,GAAA;AAAA,QACT,UAAA,CAAA,MAAA;AAEA,QAAA;AACE,OAAI,CAAA;AACF,MAAA,IAAA,SAAA,CAAA,aAA4B,CAAA,EAAA;AAAA,QAC9B,gBAAA,CAAA,IAAA,CAAA,CAAA;AAAA,QACF,KAAA,CAAA,eAAA,EAAA,CAAA;AAEA,QAAA,uBAA2B;AACzB,OAAkB;AAElB,MAAI,IAAA,CAAA,UAAA,CAAA,iBAAgC,CAAA,KAAA,EAAA,UAAA,CAAA,WAAA,CAAA,CAAA,QAAA,CAAA,IAAA,CAAA,IAAA,aAAA,CAAA,KAAA,KAAA,IAAA,IAAA,aAAA,CAAA,KAAA,KAAA,IAAA,EAAA;AAClC,QAAA,KAAA,CAAA;AAAgC,QAClC,IAAA,CAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,CAAA;AAAA,OACF;AAEA,KAAM,CAAA;AACJ,IAAM,MAAA,gBAAW,GAAA,CAAA,IAAA,KAAA;AACjB,MAAA,IAAA,EAAM,CAAY;AAAA,MAAA,MACL,EAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,KAAA,EAAA,IAAA,EAAA,GAAA,EAAA,MAAA,EAAA,QAAA,EAAA,GAAA,UAAA,CAAA;AAAA,MAAA,MACA,OAAA,GAAA;AAAA,QACX,IAAW,EAAA;AAAA,UACA,CAAA,EAAA,GAAA,CAAA,CAAA;AAAA,UACA,CAAA,IAAA,GAAA,CAAA;AAAA,UACA,CAAA,IAAA,GAAA,CAAA,CAAA;AAAA,UACA,CAAA,KAAA,GAAA,CAAA;AAAA,UACA,MAAA,EAAA,CAAA,IAAA,EAAA,IAAA,KAAA,IAAA,CAAA,WAAA,CAAA,IAAA,CAAA,WAAA,EAAA,GAAA,IAAA,CAAA;AAAA,SACb;AACA,QAAI,KAAA,EAAA;AACF,UAAA,CAAA,EAAA,GAAA,CAAA,CAAA;AACA,UAAA,CAAA,IAAsB,GAAA,CAAA;AACtB,UAAA,CAAA,IAAqB,GAAA,CAAA,CAAA;AAAA,UACvB,CAAA,KAAA,GAAA,CAAA;AACA,UACE,MAAY,EAAA,CAAA,IAAA,EAAA,IAAO,kBAAkB,CAAA,IAAA,CAAA,iBAAwB,CAAA;AAAA,SAC3D;AAAA,YAEY,EAAA;AAGd,UAAA,CAAA,EAAA,GAAqB,CAAA,CAAA;AACrB,UAAK,CAAA,IAAA,GAAA,CAAA;AAAsB,UAC7B,CAAA,IAAA,GAAA,CAAA,CAAA;AAAA,UACF,CAAA,KAAA,GAAA,CAAA;AAEA,UAAM,MAAA,EAAA,CAAA,IAAA,EAAA,IAAmB,KAAkB,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,OAAA,EAAA,GAAA,IAAA,GAAA,CAAA,CAAA;AAazC,SAAM;AACN,QAAA,IAAM,EAA6B;AAAA,UAC3B,CAAA,EAAA,GAAA,CAAA,CAAA;AAAA,UACJ,CAAC,IAAK,GAAA,CAAA;AAAA,UACN,CAAC,IAAI,GAAG,CAAA,CAAA;AAAA,UACR,CAAC,KAAO,GAAA,CAAA;AAAA,UACR,CAAC,OAAQ,CAAA,IAAA,KAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AAAA,UACT,CAAA,GAAA,GAAA,CAAQ,IAAa,KAAA,CAAA,IAAA,CAAA,MACd,EAAY,GAAA,CAAA;AAAyB,UAC9C,CAAA,MAAA,GAAA,CAAA,IAAA,KAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,WAAA,EAAA,EAAA,IAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,CAAA,OAAA,EAAA;AAAA,UACO,CAAA,QAAA,GAAA,CAAA,IAAA,KAAA,IAAA,IAAA,CAAA,IAAA,CAAA,WAAA,EAAA,EAAA,IAAA,CAAA,QAAA,EAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAA,OAAA,EAAA;AAAA,UACL,MAAM,EAAA,CAAA,IAAA,EAAA,IAAA,KAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,OAAA,EAAA,GAAA,IAAA,CAAA;AAAA,SACN;AAAQ,OACR,CAAA;AAAQ,MACR,aAAS,GAAA,SAAA,CAAA,KAAA,CAAA,MAAA,EAAA,CAAA;AAAA,MACT,OAAA,IAAA,CAAQ,GAAa,CAAA,SAAA,CAAA,UACL,CAAA,OAAA,EAAA,MAAc,EAAA,IAAA,CAAA,CAAI,GAAI,CAAA,EAAA;AAAA,QACxC,MAAA,GAAA,GAAA,OAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AAAA,QACA,IAAM,CAAA,GAAA;AAAA,UACJ,OAAM;AAAA,QACN,GAAC,OAAO,CAAA,OAAA,EAAA,UAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,OAAA,CAAA,GAAA,CAAA,EAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA,IAAA,GAAA,EAAA,GAAA,CAAA,CAAA,CAAA;AAAA,QACR,gBAAQ,IAAA,YAAA,CAAA,OAAA,CAAA,EAAA;AAAA,UACR,MAAM;AAAG,SACT;AACwC,QAC1C,MAAA,MAAA,GAAA,KAAA,CAAA,OAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,QACA,SAAM,CAAA,KAAA,GAAA,MAAA,CAAA;AAAA,QACJ,WAAM,CAAA,MAAA,EAAA,MAAA,EAAA,IAAA,CAAA,CAAA;AAAA,QACN;AAAQ,OACR;AAAQ,KACR,CAAA;AAAS,IAAA,MACT,iBAAwB,QAAa,KAAA;AAAA,MACrC,WAAO,CAAC,cAAgB,WAAgB,CAAA,KAAA,CAAA,MAAA,EAAA,EAAA,IAAA,EAAA,WAAA,CAAA,KAAA,CAAA,CAAA;AAAA,KAAA,CAAA;AAEoB,IAAA,KAAA,CAC5D,MAAS,aAAI,CAAA,OACF,CAAA,GAAA,KAAK;AAA+C,MAC/D,IAAA,CAAA,OAAQ,EAAa,MAAA,CAAA,CAAA,YAA8B,CAAA,EAAA;AAAqB,QAC1E,WAAA,CAAA,KAAA,GAAA,GAAA,CAAA;AAAA,QACF,OAAA;AAEA,OAAM,MAAA,IAAA,GAAA,KAAoB,OAAA,EAAA;AAC1B,QAAO,WAAS,CAAA,KAAA,GAAA,MAAgB,CAAA;AAC9B,QAAM,OAAA;AACN,OAAA,MAAK,IAAK,GAAA,KAAA,QAAA,EAAA;AACV,QAAI,WAAA,CAAA,KAAA,GAAA,OAAA,CAAA;AAAA,QACF,OAAA;AAAA,OAAA;AAG6B,MAC/B,WAAA,CAAA,KAAA,GAAA,MAAA,CAAA;AACA,KAAI,EAAA,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,CAAgB;AAClB,IAAA,KAAA,CAAA,MAAA,WAAA,CAAA,KAAA,EAAA,MAAA;AAAA,MACF,MAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,MAAA,CAAA,YAAA,EAAA,CAAA;AACA,KAAA,CAAA,CAAA;AACA,IAAA,KAAA,CAAA,MAAA,YAAkB,CAAA,KAAA,EAAA,CAAA,GAAA,KAAA;AAClB,MAAY,IAAA,GAAA,EAAA;AACZ,QAAA,SAAA,CAAA,KAAA,GAAA,eAAA,EAAA,CAAA;AAAA,OACF;AAAA,KACF,EAAA,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AAEA,IAAM,KAAA,CAAA,MAAA,KAAA,CAAA,WAAgD,EAAA,CAAA,GAAA,KAAA;AACpD,MAAA,IAAA,GAAA,EAAA;AAA6E,QAC/E,IAAA,cAAA,CAAA,KAAA;AAEA,UAAA,OAAA;AAAA,YACQ,OAAc,CAAA,GAAA,CAAA;AAAA,UACX,OAAA;AACP,QAAA,SAAc,CAAA,KAAA,GAAA,GAAQ,CAAA;AACpB,OAAA,MAAA;AACA,QAAA,SAAA,CAAA,KAAA,GAAA,eAAA,EAAA,CAAA;AAAA,OACF;AACE,KAAA,EAAA,EAAA,SAAA,EAAA,IAAoB,EAAA,CAAA,CAAA;AACpB,IAAA,WAAA,CAAA,mBAAA,EAAA,CAAA,cAAA,EAAA,YAAA,CAAA,CAAA,CAAA;AAAA,IACF,WAAA,CAAA,mBAA6B,EAAA,CAAA,gBAAA,EAAA,cAAA,CAAA,CAAA,CAAA;AAC3B,IAAA,WAAA,CAAA,mBAAoB,EAAA,CAAA,gBAAA,EAAA,cAAA,CAAA,CAAA,CAAA;AACpB,IAAA,WAAA,CAAA,mBAAA,EAAA,CAAA,mBAAA,EAAA,kBAAA,CAAA,CAAA,CAAA;AAAA,IACF,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AACA,MAAA,OAAAG,SAAoB,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,QACtB,KAAA,EAAAC,cAAA,CAAA;AAAA,oBACkB,CAAA,CAAA,CAAA,EAAA;AAAA,UACpBC,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA;AAEA,UAAA;AAAA,YACQ,aAAY,EAAA,IAAA,CAAA,MAAA,CAAA,OAAA,IAAAA,KAAA,CAAA,YAAA,CAAA;AAAA,YACZ,UAAA,EAAAA,KAAA,CAAA,QAAA,CAAA;AACJ,WAAA;AAAqB,SACvB,CAAA;AAAA,OACF,EAAA;AAEA,QAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,eACqB,EAAAF,cAAA,CAAAC,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAA,CAAA;AAAA,SACV,EAAA;AACP,UAAAE,UAAS,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,EAAA;AACP,YAAA,KAAA,EAAAH,cAAkC,CAAAC,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA;AAAA,WACpC,CAAA;AAAA,UACFA,KAAA,CAAA,YAAA,CAAA,IAAAH,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA;AACkB,YACpB,KAAA,EAAAC,cAAA,CAAAC,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA;AAEA,WAAA,EAAA;AAAA,aACcH,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAAAK,QAAA,EAAA,IAAA,EAAAC,UAAA,CAAAJ,KAAA,CAAA,SAAA,CAAA,EAAA,CAAA,QAAA,EAAA,GAAA,KAAA;AAAA,cACH,OAAAH,SAAA,EAAA,EAAAC,kBAAA,CAAA,QAAA,EAAA;AACP,gBAAS,GAAA;AACP,8BAA0B;AAC1B,gBAAI,KAAA,gBAAc,CAAAE,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA;AAClB,gBAAA,OAAkB,EAAA,CAAA,MAAA,KAAA,mBAAA,CAAA,QAAA,CAAA;AAAA,eACb,EAAAK,eAAA,CAAA,QAAA,CAAA,IAAA,CAAA,EAAA,EAAA,EAAA,CAAA,SAAA,CAAA,CAAA,CAAA;AACL,aAAA,CAAA,EAAA,GAAA,CAAU;AAAwB,WACpC,EAAA,CAAA,CAAA,IAAAC,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,UACFL,kBAAA,CAAA,KAAA,EAAA;AAAA,mBACaF,cAAK,CAAAC,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA;AAAA,WACpB,EAAA;AAEA,YAAAA,KAAiC,CAAA,QAAA,CAAA,IAAAH,SAAiB,EAAA,EAAAC,kBAAA,CAAY,KAAC,EAAA;AAC/D,cAAA,GAAiC,EAAA,CAAA;AACjC,cAAA,KAAiC,EAAAC,cAAA,CAAAC,KAAmB,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,CAAA;AACpD,aAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}