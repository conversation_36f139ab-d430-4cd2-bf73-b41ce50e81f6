{"version": 3, "file": "basic-year-table.mjs", "sources": ["../../../../../../../packages/components/date-picker/src/props/basic-year-table.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { datePickerSharedProps, selectionModeWithDefault } from './shared'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\n\nexport const basicYearTableProps = buildProps({\n  ...datePickerSharedProps,\n  selectionMode: selectionModeWithDefault('year'),\n} as const)\n\nexport type BasicYearTableProps = ExtractPropTypes<typeof basicYearTableProps>\nexport type BasicYearTablePropsPublic = __ExtractPublicPropTypes<\n  typeof basicYearTableProps\n>\n"], "names": [], "mappings": ";;;AAEY,MAAC,mBAAmB,GAAG,UAAU,CAAC;AAC9C,EAAE,GAAG,qBAAqB;AAC1B,EAAE,aAAa,EAAE,wBAAwB,CAAC,MAAM,CAAC;AACjD,CAAC;;;;"}