{"version": 3, "file": "basic-month-table.mjs", "sources": ["../../../../../../../packages/components/date-picker/src/props/basic-month-table.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { datePickerSharedProps, selectionModeWithDefault } from './shared'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\n\nexport const basicMonthTableProps = buildProps({\n  ...datePickerSharedProps,\n  selectionMode: selectionModeWithDefault('month'),\n})\n\nexport type BasicMonthTableProps = ExtractPropTypes<typeof basicMonthTableProps>\nexport type BasicMonthTablePropsPublic = __ExtractPublicPropTypes<\n  typeof basicMonthTableProps\n>\n"], "names": [], "mappings": ";;;AAEY,MAAC,oBAAoB,GAAG,UAAU,CAAC;AAC/C,EAAE,GAAG,qBAAqB;AAC1B,EAAE,aAAa,EAAE,wBAAwB,CAAC,OAAO,CAAC;AAClD,CAAC;;;;"}