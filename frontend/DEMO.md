# SDL平台前端演示

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 启动开发服务器
```bash
npm run dev
```

### 3. 访问应用
打开浏览器访问：http://localhost:3000

## 演示账号

**用户名**: `admin`  
**密码**: `admin123`

## 功能演示

### 已实现的功能模块

1. **用户认证**
   - ✅ 登录页面（支持演示账号）
   - ✅ 权限控制
   - ✅ 用户信息管理

2. **安全仪表板**
   - ✅ 安全评分展示
   - ✅ 漏洞趋势图表
   - ✅ 项目安全排名
   - ✅ 最近扫描记录

3. **项目管理**
   - ✅ 项目列表和搜索
   - ✅ 项目创建和编辑
   - ✅ 扫描配置管理
   - ✅ 批量操作

4. **扫描任务管理**
   - ✅ 扫描任务列表
   - ✅ 扫描详情查看
   - ✅ 扫描结果展示

5. **漏洞管理**
   - ✅ 漏洞列表和筛选
   - ✅ 漏洞详情查看
   - ✅ 漏洞状态管理

6. **安全报告**
   - ✅ 报告生成界面
   - ✅ 报告列表管理

7. **系统管理**
   - ✅ 用户管理
   - ✅ 角色管理
   - ✅ 权限控制

### 技术特性

- ✅ 响应式设计（支持移动端）
- ✅ TypeScript类型安全
- ✅ 组件化开发
- ✅ 路由守卫和权限检查
- ✅ 统一的错误处理
- ✅ 国际化支持（中文）

## 注意事项

1. **开发模式**：当前运行在开发模式下，使用模拟数据和模拟登录
2. **数据持久化**：所有数据都是模拟数据，刷新页面后会重置
3. **API接口**：前端已经定义了完整的API接口规范，等待后端实现

## 下一步开发

1. **后端API开发**：实现Go语言的后端API服务
2. **数据库集成**：连接真实的数据库
3. **安全扫描引擎**：集成实际的安全扫描工具
4. **CI/CD集成**：与持续集成流水线集成

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **图表库**: ECharts
- **样式**: CSS3 + 响应式设计

## 项目结构

```
frontend/
├── src/
│   ├── api/           # API接口定义
│   ├── components/    # 公共组件
│   ├── layout/        # 布局组件
│   ├── router/        # 路由配置
│   ├── stores/        # 状态管理
│   ├── styles/        # 全局样式
│   ├── types/         # TypeScript类型
│   ├── utils/         # 工具函数
│   └── views/         # 页面组件
├── public/            # 静态资源
└── dist/              # 构建输出
```

## 联系方式

如有问题或建议，请联系开发团队。
